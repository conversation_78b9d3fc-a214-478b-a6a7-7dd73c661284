{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/application/use-cases/GetProductsUseCase.ts"], "sourcesContent": ["import { Product } from '../../domain/entities/Product'\nimport { ProductRepository, ProductFilters } from '../../domain/repositories/ProductRepository'\n\nexport interface GetProductsRequest {\n  filters?: ProductFilters\n  sortBy?: 'name' | 'price' | 'rating' | 'category'\n  sortOrder?: 'asc' | 'desc'\n  page?: number\n  pageSize?: number\n}\n\nexport interface GetProductsResponse {\n  products: Product[]\n  totalCount: number\n  page: number\n  pageSize: number\n  hasNextPage: boolean\n  hasPreviousPage: boolean\n}\n\nexport class GetProductsUseCase {\n  constructor(private productRepository: ProductRepository) {}\n\n  async execute(request: GetProductsRequest = {}): Promise<GetProductsResponse> {\n    try {\n      // Get all products with filters\n      const allProducts = await this.productRepository.findAll(request.filters)\n\n      // Apply sorting\n      const sortedProducts = this.sortProducts(allProducts, request.sortBy, request.sortOrder)\n\n      // Apply pagination\n      const page = request.page || 1\n      const pageSize = request.pageSize || 20\n      const startIndex = (page - 1) * pageSize\n      const endIndex = startIndex + pageSize\n\n      const paginatedProducts = sortedProducts.slice(startIndex, endIndex)\n\n      return {\n        products: paginatedProducts,\n        totalCount: allProducts.length,\n        page,\n        pageSize,\n        hasNextPage: endIndex < allProducts.length,\n        hasPreviousPage: page > 1,\n      }\n    } catch (error) {\n      throw new Error(`Failed to get products: ${error instanceof Error ? error.message : 'Unknown error'}`)\n    }\n  }\n\n  private sortProducts(\n    products: Product[], \n    sortBy?: string, \n    sortOrder: 'asc' | 'desc' = 'asc'\n  ): Product[] {\n    if (!sortBy) return products\n\n    const sorted = [...products].sort((a, b) => {\n      let comparison = 0\n\n      switch (sortBy) {\n        case 'name':\n          comparison = a.name.localeCompare(b.name)\n          break\n        case 'price':\n          comparison = a.price.amount - b.price.amount\n          break\n        case 'rating':\n          comparison = a.rating - b.rating\n          break\n        case 'category':\n          comparison = a.category.localeCompare(b.category)\n          break\n        default:\n          return 0\n      }\n\n      return sortOrder === 'desc' ? -comparison : comparison\n    })\n\n    return sorted\n  }\n}\n"], "names": [], "mappings": ";;;AAoBO,MAAM;;IACX,YAAY,AAAQ,iBAAoC,CAAE;aAAtC,oBAAA;IAAuC;IAE3D,MAAM,QAAQ,UAA8B,CAAC,CAAC,EAAgC;QAC5E,IAAI;YACF,gCAAgC;YAChC,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,OAAO;YAExE,gBAAgB;YAChB,MAAM,iBAAiB,IAAI,CAAC,YAAY,CAAC,aAAa,QAAQ,MAAM,EAAE,QAAQ,SAAS;YAEvF,mBAAmB;YACnB,MAAM,OAAO,QAAQ,IAAI,IAAI;YAC7B,MAAM,WAAW,QAAQ,QAAQ,IAAI;YACrC,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAE9B,MAAM,oBAAoB,eAAe,KAAK,CAAC,YAAY;YAE3D,OAAO;gBACL,UAAU;gBACV,YAAY,YAAY,MAAM;gBAC9B;gBACA;gBACA,aAAa,WAAW,YAAY,MAAM;gBAC1C,iBAAiB,OAAO;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACvG;IACF;IAEQ,aACN,QAAmB,EACnB,MAAe,EACf,YAA4B,KAAK,EACtB;QACX,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,SAAS;eAAI;SAAS,CAAC,IAAI,CAAC,CAAC,GAAG;YACpC,IAAI,aAAa;YAEjB,OAAQ;gBACN,KAAK;oBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;oBACxC;gBACF,KAAK;oBACH,aAAa,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,MAAM;oBAC5C;gBACF,KAAK;oBACH,aAAa,EAAE,MAAM,GAAG,EAAE,MAAM;oBAChC;gBACF,KAAK;oBACH,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ;oBAChD;gBACF;oBACE,OAAO;YACX;YAEA,OAAO,cAAc,SAAS,CAAC,aAAa;QAC9C;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/domain/value-objects/Price.ts"], "sourcesContent": ["export class Price {\n  private constructor(\n    private readonly _amount: number,\n    private readonly _currency: string\n  ) {\n    this.validate()\n  }\n\n  static create(amount: number, currency: string = 'INR'): Price {\n    return new Price(amount, currency)\n  }\n\n  private validate(): void {\n    if (this._amount < 0) {\n      throw new Error('Price amount cannot be negative')\n    }\n    if (!this._currency || this._currency.trim().length === 0) {\n      throw new Error('Currency is required')\n    }\n    if (this._currency.length !== 3) {\n      throw new Error('Currency must be a 3-letter code')\n    }\n  }\n\n  get amount(): number {\n    return this._amount\n  }\n\n  get currency(): string {\n    return this._currency\n  }\n\n  format(): string {\n    const currencySymbols: Record<string, string> = {\n      INR: '₹',\n      USD: '$',\n      EUR: '€',\n      GBP: '£',\n    }\n\n    const symbol = currencySymbols[this._currency] || this._currency\n    return `${symbol}${this._amount.toLocaleString()}`\n  }\n\n  equals(other: Price): boolean {\n    return this._amount === other._amount && this._currency === other._currency\n  }\n\n  add(other: Price): Price {\n    if (this._currency !== other._currency) {\n      throw new Error('Cannot add prices with different currencies')\n    }\n    return new Price(this._amount + other._amount, this._currency)\n  }\n\n  multiply(factor: number): Price {\n    if (factor < 0) {\n      throw new Error('Factor cannot be negative')\n    }\n    return new Price(this._amount * factor, this._currency)\n  }\n\n  isGreaterThan(other: Price): boolean {\n    if (this._currency !== other._currency) {\n      throw new Error('Cannot compare prices with different currencies')\n    }\n    return this._amount > other._amount\n  }\n\n  isLessThan(other: Price): boolean {\n    if (this._currency !== other._currency) {\n      throw new Error('Cannot compare prices with different currencies')\n    }\n    return this._amount < other._amount\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,MAAM;;;IACX,YACE,AAAiB,OAAe,EAChC,AAAiB,SAAiB,CAClC;aAFiB,UAAA;aACA,YAAA;QAEjB,IAAI,CAAC,QAAQ;IACf;IAEA,OAAO,OAAO,MAAc,EAAE,WAAmB,KAAK,EAAS;QAC7D,OAAO,IAAI,MAAM,QAAQ;IAC3B;IAEQ,WAAiB;QACvB,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG;YACpB,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;YAC/B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,IAAI,SAAiB;QACnB,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,SAAiB;QACf,MAAM,kBAA0C;YAC9C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,MAAM,SAAS,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS;QAChE,OAAO,GAAG,SAAS,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI;IACpD;IAEA,OAAO,KAAY,EAAW;QAC5B,OAAO,IAAI,CAAC,OAAO,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,SAAS;IAC7E;IAEA,IAAI,KAAY,EAAS;QACvB,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,SAAS,EAAE;YACtC,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,MAAM,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO,EAAE,IAAI,CAAC,SAAS;IAC/D;IAEA,SAAS,MAAc,EAAS;QAC9B,IAAI,SAAS,GAAG;YACd,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,MAAM,IAAI,CAAC,OAAO,GAAG,QAAQ,IAAI,CAAC,SAAS;IACxD;IAEA,cAAc,KAAY,EAAW;QACnC,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,SAAS,EAAE;YACtC,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;IACrC;IAEA,WAAW,KAAY,EAAW;QAChC,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,SAAS,EAAE;YACtC,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;IACrC;AACF", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/domain/entities/Product.ts"], "sourcesContent": ["import { Price } from '../value-objects/Price'\n\nexport interface ProductProps {\n  id: string\n  name: string\n  description: string\n  price: Price\n  imageUrl: string\n  category: string\n  rating: number\n  reviewCount: number\n  duration: string\n  isAvailable: boolean\n  tags?: string[]\n}\n\nexport class Product {\n  private constructor(private props: ProductProps) {\n    this.validateProps(props)\n  }\n\n  static create(props: ProductProps): Product {\n    return new Product(props)\n  }\n\n  static fromJson(data: any): Product {\n    return new Product({\n      id: data.id,\n      name: data.name,\n      description: data.description,\n      price: Price.create(data.price, data.currency || 'INR'),\n      imageUrl: data.imageUrl,\n      category: data.category,\n      rating: data.rating,\n      reviewCount: data.reviewCount,\n      duration: data.duration,\n      isAvailable: data.isAvailable ?? true,\n      tags: data.tags || [],\n    })\n  }\n\n  private validateProps(props: ProductProps): void {\n    if (!props.id || props.id.trim().length === 0) {\n      throw new Error('Product ID is required')\n    }\n    if (!props.name || props.name.trim().length === 0) {\n      throw new Error('Product name is required')\n    }\n    if (!props.description || props.description.trim().length === 0) {\n      throw new Error('Product description is required')\n    }\n    if (!props.imageUrl || props.imageUrl.trim().length === 0) {\n      throw new Error('Product image URL is required')\n    }\n    if (!props.category || props.category.trim().length === 0) {\n      throw new Error('Product category is required')\n    }\n    if (props.rating < 0 || props.rating > 5) {\n      throw new Error('Product rating must be between 0 and 5')\n    }\n    if (props.reviewCount < 0) {\n      throw new Error('Product review count cannot be negative')\n    }\n  }\n\n  get id(): string {\n    return this.props.id\n  }\n\n  get name(): string {\n    return this.props.name\n  }\n\n  get description(): string {\n    return this.props.description\n  }\n\n  get price(): Price {\n    return this.props.price\n  }\n\n  get imageUrl(): string {\n    return this.props.imageUrl\n  }\n\n  get category(): string {\n    return this.props.category\n  }\n\n  get rating(): number {\n    return this.props.rating\n  }\n\n  get reviewCount(): number {\n    return this.props.reviewCount\n  }\n\n  get duration(): string {\n    return this.props.duration\n  }\n\n  get isAvailable(): boolean {\n    return this.props.isAvailable\n  }\n\n  get tags(): string[] {\n    return this.props.tags || []\n  }\n\n  get formattedPrice(): string {\n    return this.props.price.format()\n  }\n\n  get formattedRating(): string {\n    return `${this.props.rating} (${this.props.reviewCount} reviews)`\n  }\n\n  equals(other: Product): boolean {\n    return this.props.id === other.props.id\n  }\n\n  toJson(): any {\n    return {\n      id: this.props.id,\n      name: this.props.name,\n      description: this.props.description,\n      price: this.props.price.amount,\n      currency: this.props.price.currency,\n      imageUrl: this.props.imageUrl,\n      category: this.props.category,\n      rating: this.props.rating,\n      reviewCount: this.props.reviewCount,\n      duration: this.props.duration,\n      isAvailable: this.props.isAvailable,\n      tags: this.props.tags,\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAgBO,MAAM;;IACX,YAAoB,AAAQ,KAAmB,CAAE;aAArB,QAAA;QAC1B,IAAI,CAAC,aAAa,CAAC;IACrB;IAEA,OAAO,OAAO,KAAmB,EAAW;QAC1C,OAAO,IAAI,QAAQ;IACrB;IAEA,OAAO,SAAS,IAAS,EAAW;QAClC,OAAO,IAAI,QAAQ;YACjB,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,OAAO,6IAAA,CAAA,QAAK,CAAC,MAAM,CAAC,KAAK,KAAK,EAAE,KAAK,QAAQ,IAAI;YACjD,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,MAAM;YACnB,aAAa,KAAK,WAAW;YAC7B,UAAU,KAAK,QAAQ;YACvB,aAAa,KAAK,WAAW,IAAI;YACjC,MAAM,KAAK,IAAI,IAAI,EAAE;QACvB;IACF;IAEQ,cAAc,KAAmB,EAAQ;QAC/C,IAAI,CAAC,MAAM,EAAE,IAAI,MAAM,EAAE,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC7C,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACjD,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,GAAG;YACxC,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,MAAM,WAAW,GAAG,GAAG;YACzB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,IAAI,KAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;IACtB;IAEA,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IACxB;IAEA,IAAI,cAAsB;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW;IAC/B;IAEA,IAAI,QAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;IACzB;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAEA,IAAI,SAAiB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IAEA,IAAI,cAAsB;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW;IAC/B;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAEA,IAAI,cAAuB;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW;IAC/B;IAEA,IAAI,OAAiB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;IAC9B;IAEA,IAAI,iBAAyB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM;IAChC;IAEA,IAAI,kBAA0B;QAC5B,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC;IACnE;IAEA,OAAO,KAAc,EAAW;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC,EAAE;IACzC;IAEA,SAAc;QACZ,OAAO;YACL,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YACjB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,aAAa,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM;YAC9B,UAAU,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ;YACnC,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,aAAa,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,aAAa,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/domain/repositories/ProductRepository.ts"], "sourcesContent": ["import { Product } from '../entities/Product'\n\nexport interface ProductFilters {\n  category?: string\n  minPrice?: number\n  maxPrice?: number\n  minRating?: number\n  searchTerm?: string\n  tags?: string[]\n}\n\nexport interface ProductRepository {\n  findAll(filters?: ProductFilters): Promise<Product[]>\n  findById(id: string): Promise<Product | null>\n  findByCategory(category: string): Promise<Product[]>\n  search(searchTerm: string): Promise<Product[]>\n  getCategories(): Promise<string[]>\n}\n\nexport interface ProductRepositoryError extends Error {\n  code: string\n  retryable: boolean\n}\n\nexport class ProductNotFoundError extends Error implements ProductRepositoryError {\n  code = 'PRODUCT_NOT_FOUND'\n  retryable = false\n\n  constructor(productId: string) {\n    super(`Product with ID ${productId} not found`)\n    this.name = 'ProductNotFoundError'\n  }\n}\n\nexport class ProductRepositoryConnectionError extends Error implements ProductRepositoryError {\n  code = 'CONNECTION_ERROR'\n  retryable = true\n\n  constructor(message: string) {\n    super(message)\n    this.name = 'ProductRepositoryConnectionError'\n  }\n}\n\nexport class ProductRepositoryTimeoutError extends Error implements ProductRepositoryError {\n  code = 'TIMEOUT_ERROR'\n  retryable = true\n\n  constructor(message: string) {\n    super(message)\n    this.name = 'ProductRepositoryTimeoutError'\n  }\n}\n"], "names": [], "mappings": ";;;;;AAwBO,MAAM,6BAA6B;IACxC,OAAO,oBAAmB;IAC1B,YAAY,MAAK;IAEjB,YAAY,SAAiB,CAAE;QAC7B,KAAK,CAAC,CAAC,gBAAgB,EAAE,UAAU,UAAU,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,yCAAyC;IACpD,OAAO,mBAAkB;IACzB,YAAY,KAAI;IAEhB,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,sCAAsC;IACjD,OAAO,gBAAe;IACtB,YAAY,KAAI;IAEhB,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/infrastructure/api/CircuitBreaker.ts"], "sourcesContent": ["export enum CircuitBreakerState {\n  CLOSED = 'CLOSED',\n  OPEN = 'OPEN',\n  HALF_OPEN = 'HALF_OPEN',\n}\n\nexport interface CircuitBreakerConfig {\n  failureThreshold: number\n  recoveryTimeout: number\n  monitoringPeriod: number\n}\n\nexport class CircuitBreaker {\n  private state: CircuitBreakerState = CircuitBreakerState.CLOSED\n  private failureCount = 0\n  private lastFailureTime: number | null = null\n  private successCount = 0\n\n  constructor(private config: CircuitBreakerConfig) {}\n\n  async execute<T>(operation: () => Promise<T>): Promise<T> {\n    if (this.state === CircuitBreakerState.OPEN) {\n      if (this.shouldAttemptReset()) {\n        this.state = CircuitBreakerState.HALF_OPEN\n        this.successCount = 0\n      } else {\n        throw new Error('Circuit breaker is OPEN')\n      }\n    }\n\n    try {\n      const result = await operation()\n      this.onSuccess()\n      return result\n    } catch (error) {\n      this.onFailure()\n      throw error\n    }\n  }\n\n  private onSuccess(): void {\n    this.failureCount = 0\n    this.lastFailureTime = null\n\n    if (this.state === CircuitBreakerState.HALF_OPEN) {\n      this.successCount++\n      if (this.successCount >= 3) {\n        this.state = CircuitBreakerState.CLOSED\n      }\n    }\n  }\n\n  private onFailure(): void {\n    this.failureCount++\n    this.lastFailureTime = Date.now()\n\n    if (this.failureCount >= this.config.failureThreshold) {\n      this.state = CircuitBreakerState.OPEN\n    }\n  }\n\n  private shouldAttemptReset(): boolean {\n    if (this.lastFailureTime === null) return false\n    return Date.now() - this.lastFailureTime >= this.config.recoveryTimeout\n  }\n\n  getState(): CircuitBreakerState {\n    return this.state\n  }\n\n  getFailureCount(): number {\n    return this.failureCount\n  }\n\n  reset(): void {\n    this.state = CircuitBreakerState.CLOSED\n    this.failureCount = 0\n    this.lastFailureTime = null\n    this.successCount = 0\n  }\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,6CAAA;;;;WAAA;;AAYL,MAAM;;IACH,MAAuD;IACvD,aAAgB;IAChB,gBAAqC;IACrC,aAAgB;IAExB,YAAY,AAAQ,MAA4B,CAAE;aAA9B,SAAA;aALZ;aACA,eAAe;aACf,kBAAiC;aACjC,eAAe;IAE4B;IAEnD,MAAM,QAAW,SAA2B,EAAc;QACxD,IAAI,IAAI,CAAC,KAAK,aAA+B;YAC3C,IAAI,IAAI,CAAC,kBAAkB,IAAI;gBAC7B,IAAI,CAAC,KAAK;gBACV,IAAI,CAAC,YAAY,GAAG;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,IAAI,CAAC,SAAS;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,SAAS;YACd,MAAM;QACR;IACF;IAEQ,YAAkB;QACxB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,eAAe,GAAG;QAEvB,IAAI,IAAI,CAAC,KAAK,kBAAoC;YAChD,IAAI,CAAC,YAAY;YACjB,IAAI,IAAI,CAAC,YAAY,IAAI,GAAG;gBAC1B,IAAI,CAAC,KAAK;YACZ;QACF;IACF;IAEQ,YAAkB;QACxB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG;QAE/B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;YACrD,IAAI,CAAC,KAAK;QACZ;IACF;IAEQ,qBAA8B;QACpC,IAAI,IAAI,CAAC,eAAe,KAAK,MAAM,OAAO;QAC1C,OAAO,KAAK,GAAG,KAAK,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe;IACzE;IAEA,WAAgC;QAC9B,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,kBAA0B;QACxB,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,QAAc;QACZ,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,YAAY,GAAG;IACtB;AACF", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/infrastructure/api/RetryPolicy.ts"], "sourcesContent": ["export interface RetryConfig {\n  maxAttempts: number\n  baseDelay: number\n  maxDelay: number\n  backoffMultiplier: number\n  retryableErrors: string[]\n}\n\nexport class RetryPolicy {\n  constructor(private config: RetryConfig) {}\n\n  async execute<T>(operation: () => Promise<T>): Promise<T> {\n    let lastError: Error\n    \n    for (let attempt = 1; attempt <= this.config.maxAttempts; attempt++) {\n      try {\n        return await operation()\n      } catch (error) {\n        lastError = error as Error\n        \n        if (!this.isRetryable(error as Error) || attempt === this.config.maxAttempts) {\n          throw error\n        }\n\n        const delay = this.calculateDelay(attempt)\n        await this.sleep(delay)\n      }\n    }\n\n    throw lastError!\n  }\n\n  private isRetryable(error: Error): boolean {\n    return this.config.retryableErrors.some(retryableError => \n      error.message.includes(retryableError) || \n      error.name.includes(retryableError)\n    )\n  }\n\n  private calculateDelay(attempt: number): number {\n    const delay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt - 1)\n    return Math.min(delay, this.config.maxDelay)\n  }\n\n  private sleep(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms))\n  }\n}\n\nexport const defaultRetryConfig: RetryConfig = {\n  maxAttempts: 3,\n  baseDelay: 1000,\n  maxDelay: 10000,\n  backoffMultiplier: 2,\n  retryableErrors: [\n    'NetworkError',\n    'TimeoutError',\n    'CONNECTION_ERROR',\n    'TIMEOUT_ERROR',\n    'fetch',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAQO,MAAM;;IACX,YAAY,AAAQ,MAAmB,CAAE;aAArB,SAAA;IAAsB;IAE1C,MAAM,QAAW,SAA2B,EAAc;QACxD,IAAI;QAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,UAAW;YACnE,IAAI;gBACF,OAAO,MAAM;YACf,EAAE,OAAO,OAAO;gBACd,YAAY;gBAEZ,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAmB,YAAY,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;oBAC5E,MAAM;gBACR;gBAEA,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC;gBAClC,MAAM,IAAI,CAAC,KAAK,CAAC;YACnB;QACF;QAEA,MAAM;IACR;IAEQ,YAAY,KAAY,EAAW;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,iBACtC,MAAM,OAAO,CAAC,QAAQ,CAAC,mBACvB,MAAM,IAAI,CAAC,QAAQ,CAAC;IAExB;IAEQ,eAAe,OAAe,EAAU;QAC9C,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,UAAU;QACxF,OAAO,KAAK,GAAG,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ;IAC7C;IAEQ,MAAM,EAAU,EAAiB;QACvC,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IACpD;AACF;AAEO,MAAM,qBAAkC;IAC7C,aAAa;IACb,WAAW;IACX,UAAU;IACV,mBAAmB;IACnB,iBAAiB;QACf;QACA;QACA;QACA;QACA;KACD;AACH", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/infrastructure/repositories/MockProductRepository.ts"], "sourcesContent": ["import { Product } from '../../domain/entities/Product'\nimport { \n  ProductRepository, \n  ProductFilters,\n  ProductNotFoundError,\n  ProductRepositoryConnectionError,\n  ProductRepositoryTimeoutError\n} from '../../domain/repositories/ProductRepository'\nimport { CircuitBreaker } from '../api/CircuitBreaker'\nimport { RetryPolicy, defaultRetryConfig } from '../api/RetryPolicy'\nimport productsData from '../../data/products.json'\n\nexport class MockProductRepository implements ProductRepository {\n  private products: Product[]\n  private circuitBreaker: CircuitBreaker\n  private retryPolicy: RetryPolicy\n  private simulateFailure: boolean = false\n  private failureRate: number = 0.1 // 10% failure rate for testing\n\n  constructor() {\n    this.products = productsData.map(data => Product.fromJson(data))\n    this.circuitBreaker = new CircuitBreaker({\n      failureThreshold: 5,\n      recoveryTimeout: 30000, // 30 seconds\n      monitoringPeriod: 60000, // 1 minute\n    })\n    this.retryPolicy = new RetryPolicy(defaultRetryConfig)\n  }\n\n  async findAll(filters?: ProductFilters): Promise<Product[]> {\n    return this.circuitBreaker.execute(async () => {\n      return this.retryPolicy.execute(async () => {\n        await this.simulateNetworkDelay()\n        this.maybeThrowError()\n\n        let filteredProducts = [...this.products]\n\n        if (filters) {\n          if (filters.category) {\n            filteredProducts = filteredProducts.filter(p => \n              p.category.toLowerCase() === filters.category!.toLowerCase()\n            )\n          }\n\n          if (filters.minPrice !== undefined) {\n            filteredProducts = filteredProducts.filter(p => \n              p.price.amount >= filters.minPrice!\n            )\n          }\n\n          if (filters.maxPrice !== undefined) {\n            filteredProducts = filteredProducts.filter(p => \n              p.price.amount <= filters.maxPrice!\n            )\n          }\n\n          if (filters.minRating !== undefined) {\n            filteredProducts = filteredProducts.filter(p => \n              p.rating >= filters.minRating!\n            )\n          }\n\n          if (filters.searchTerm) {\n            const searchTerm = filters.searchTerm.toLowerCase()\n            filteredProducts = filteredProducts.filter(p => \n              p.name.toLowerCase().includes(searchTerm) ||\n              p.description.toLowerCase().includes(searchTerm) ||\n              p.tags.some(tag => tag.toLowerCase().includes(searchTerm))\n            )\n          }\n\n          if (filters.tags && filters.tags.length > 0) {\n            filteredProducts = filteredProducts.filter(p => \n              filters.tags!.some(tag => \n                p.tags.some(productTag => \n                  productTag.toLowerCase() === tag.toLowerCase()\n                )\n              )\n            )\n          }\n        }\n\n        return filteredProducts\n      })\n    })\n  }\n\n  async findById(id: string): Promise<Product | null> {\n    return this.circuitBreaker.execute(async () => {\n      return this.retryPolicy.execute(async () => {\n        await this.simulateNetworkDelay()\n        this.maybeThrowError()\n\n        const product = this.products.find(p => p.id === id)\n        return product || null\n      })\n    })\n  }\n\n  async findByCategory(category: string): Promise<Product[]> {\n    return this.findAll({ category })\n  }\n\n  async search(searchTerm: string): Promise<Product[]> {\n    return this.findAll({ searchTerm })\n  }\n\n  async getCategories(): Promise<string[]> {\n    return this.circuitBreaker.execute(async () => {\n      return this.retryPolicy.execute(async () => {\n        await this.simulateNetworkDelay()\n        this.maybeThrowError()\n\n        const categories = [...new Set(this.products.map(p => p.category))]\n        return categories.sort()\n      })\n    })\n  }\n\n  // Testing utilities\n  setSimulateFailure(simulate: boolean): void {\n    this.simulateFailure = simulate\n  }\n\n  setFailureRate(rate: number): void {\n    this.failureRate = Math.max(0, Math.min(1, rate))\n  }\n\n  resetCircuitBreaker(): void {\n    this.circuitBreaker.reset()\n  }\n\n  getCircuitBreakerState(): string {\n    return this.circuitBreaker.getState()\n  }\n\n  private async simulateNetworkDelay(): Promise<void> {\n    // Simulate realistic network delay (50-200ms)\n    const delay = Math.random() * 150 + 50\n    await new Promise(resolve => setTimeout(resolve, delay))\n  }\n\n  private maybeThrowError(): void {\n    if (this.simulateFailure || Math.random() < this.failureRate) {\n      const errorTypes = [\n        () => new ProductRepositoryConnectionError('Network connection failed'),\n        () => new ProductRepositoryTimeoutError('Request timeout'),\n        () => new Error('Internal server error'),\n      ]\n      \n      const randomError = errorTypes[Math.floor(Math.random() * errorTypes.length)]\n      throw randomError()\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAOA;AACA;AACA;;;;;;AAEO,MAAM;IACH,SAAmB;IACnB,eAA8B;IAC9B,YAAwB;IACxB,kBAA2B,MAAK;IAChC,cAAsB,IAAI,+BAA+B;KAAhC;IAEjC,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,+FAAA,CAAA,UAAY,CAAC,GAAG,CAAC,CAAA,OAAQ,uIAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;QAC1D,IAAI,CAAC,cAAc,GAAG,IAAI,iJAAA,CAAA,iBAAc,CAAC;YACvC,kBAAkB;YAClB,iBAAiB;YACjB,kBAAkB;QACpB;QACA,IAAI,CAAC,WAAW,GAAG,IAAI,8IAAA,CAAA,cAAW,CAAC,8IAAA,CAAA,qBAAkB;IACvD;IAEA,MAAM,QAAQ,OAAwB,EAAsB;QAC1D,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACjC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBAC9B,MAAM,IAAI,CAAC,oBAAoB;gBAC/B,IAAI,CAAC,eAAe;gBAEpB,IAAI,mBAAmB;uBAAI,IAAI,CAAC,QAAQ;iBAAC;gBAEzC,IAAI,SAAS;oBACX,IAAI,QAAQ,QAAQ,EAAE;wBACpB,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IACzC,EAAE,QAAQ,CAAC,WAAW,OAAO,QAAQ,QAAQ,CAAE,WAAW;oBAE9D;oBAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;wBAClC,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IACzC,EAAE,KAAK,CAAC,MAAM,IAAI,QAAQ,QAAQ;oBAEtC;oBAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;wBAClC,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IACzC,EAAE,KAAK,CAAC,MAAM,IAAI,QAAQ,QAAQ;oBAEtC;oBAEA,IAAI,QAAQ,SAAS,KAAK,WAAW;wBACnC,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IACzC,EAAE,MAAM,IAAI,QAAQ,SAAS;oBAEjC;oBAEA,IAAI,QAAQ,UAAU,EAAE;wBACtB,MAAM,aAAa,QAAQ,UAAU,CAAC,WAAW;wBACjD,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IACzC,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC9B,EAAE,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eACrC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;oBAElD;oBAEA,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;wBAC3C,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IACzC,QAAQ,IAAI,CAAE,IAAI,CAAC,CAAA,MACjB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA,aACV,WAAW,WAAW,OAAO,IAAI,WAAW;oBAIpD;gBACF;gBAEA,OAAO;YACT;QACF;IACF;IAEA,MAAM,SAAS,EAAU,EAA2B;QAClD,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACjC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBAC9B,MAAM,IAAI,CAAC,oBAAoB;gBAC/B,IAAI,CAAC,eAAe;gBAEpB,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACjD,OAAO,WAAW;YACpB;QACF;IACF;IAEA,MAAM,eAAe,QAAgB,EAAsB;QACzD,OAAO,IAAI,CAAC,OAAO,CAAC;YAAE;QAAS;IACjC;IAEA,MAAM,OAAO,UAAkB,EAAsB;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC;YAAE;QAAW;IACnC;IAEA,MAAM,gBAAmC;QACvC,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACjC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBAC9B,MAAM,IAAI,CAAC,oBAAoB;gBAC/B,IAAI,CAAC,eAAe;gBAEpB,MAAM,aAAa;uBAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;iBAAG;gBACnE,OAAO,WAAW,IAAI;YACxB;QACF;IACF;IAEA,oBAAoB;IACpB,mBAAmB,QAAiB,EAAQ;QAC1C,IAAI,CAAC,eAAe,GAAG;IACzB;IAEA,eAAe,IAAY,EAAQ;QACjC,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;IAC7C;IAEA,sBAA4B;QAC1B,IAAI,CAAC,cAAc,CAAC,KAAK;IAC3B;IAEA,yBAAiC;QAC/B,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ;IACrC;IAEA,MAAc,uBAAsC;QAClD,8CAA8C;QAC9C,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM;QACpC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IACnD;IAEQ,kBAAwB;QAC9B,IAAI,IAAI,CAAC,eAAe,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,WAAW,EAAE;YAC5D,MAAM,aAAa;gBACjB,IAAM,IAAI,qJAAA,CAAA,mCAAgC,CAAC;gBAC3C,IAAM,IAAI,qJAAA,CAAA,gCAA6B,CAAC;gBACxC,IAAM,IAAI,MAAM;aACjB;YAED,MAAM,cAAc,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;YAC7E,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/stores/useProductStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { Product } from '../domain/entities/Product'\nimport { ProductFilters } from '../domain/repositories/ProductRepository'\nimport { GetProductsUseCase, GetProductsResponse } from '../application/use-cases/GetProductsUseCase'\nimport { MockProductRepository } from '../infrastructure/repositories/MockProductRepository'\n\ninterface ProductStore {\n  // State\n  products: Product[]\n  loading: boolean\n  error: string | null\n  filters: ProductFilters\n  sortBy: 'name' | 'price' | 'rating' | 'category' | null\n  sortOrder: 'asc' | 'desc'\n  searchTerm: string\n  categories: string[]\n  \n  // Pagination\n  currentPage: number\n  pageSize: number\n  totalCount: number\n  hasNextPage: boolean\n  hasPreviousPage: boolean\n\n  // Actions\n  loadProducts: () => Promise<void>\n  loadCategories: () => Promise<void>\n  setFilters: (filters: Partial<ProductFilters>) => void\n  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void\n  setSearchTerm: (searchTerm: string) => void\n  setPage: (page: number) => void\n  clearFilters: () => void\n  refreshProducts: () => Promise<void>\n}\n\n// Create repository instance\nconst productRepository = new MockProductRepository()\nconst getProductsUseCase = new GetProductsUseCase(productRepository)\n\nexport const useProductStore = create<ProductStore>((set, get) => ({\n  // Initial state\n  products: [],\n  loading: false,\n  error: null,\n  filters: {},\n  sortBy: null,\n  sortOrder: 'asc',\n  searchTerm: '',\n  categories: [],\n  currentPage: 1,\n  pageSize: 20,\n  totalCount: 0,\n  hasNextPage: false,\n  hasPreviousPage: false,\n\n  // Actions\n  loadProducts: async () => {\n    const state = get()\n    set({ loading: true, error: null })\n\n    try {\n      const response: GetProductsResponse = await getProductsUseCase.execute({\n        filters: {\n          ...state.filters,\n          searchTerm: state.searchTerm || undefined,\n        },\n        sortBy: state.sortBy || undefined,\n        sortOrder: state.sortOrder,\n        page: state.currentPage,\n        pageSize: state.pageSize,\n      })\n\n      set({\n        products: response.products,\n        totalCount: response.totalCount,\n        hasNextPage: response.hasNextPage,\n        hasPreviousPage: response.hasPreviousPage,\n        loading: false,\n        error: null,\n      })\n    } catch (error) {\n      set({\n        loading: false,\n        error: error instanceof Error ? error.message : 'Failed to load products',\n        products: [],\n      })\n    }\n  },\n\n  loadCategories: async () => {\n    try {\n      const categories = await productRepository.getCategories()\n      set({ categories })\n    } catch (error) {\n      console.error('Failed to load categories:', error)\n    }\n  },\n\n  setFilters: (newFilters: Partial<ProductFilters>) => {\n    const state = get()\n    set({\n      filters: { ...state.filters, ...newFilters },\n      currentPage: 1, // Reset to first page when filters change\n    })\n    // Auto-reload products when filters change\n    get().loadProducts()\n  },\n\n  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => {\n    set({\n      sortBy: sortBy as any,\n      sortOrder,\n      currentPage: 1, // Reset to first page when sorting changes\n    })\n    // Auto-reload products when sorting changes\n    get().loadProducts()\n  },\n\n  setSearchTerm: (searchTerm: string) => {\n    set({\n      searchTerm,\n      currentPage: 1, // Reset to first page when search changes\n    })\n    // Auto-reload products when search changes\n    get().loadProducts()\n  },\n\n  setPage: (page: number) => {\n    set({ currentPage: page })\n    // Auto-reload products when page changes\n    get().loadProducts()\n  },\n\n  clearFilters: () => {\n    set({\n      filters: {},\n      searchTerm: '',\n      sortBy: null,\n      sortOrder: 'asc',\n      currentPage: 1,\n    })\n    // Auto-reload products when filters are cleared\n    get().loadProducts()\n  },\n\n  refreshProducts: async () => {\n    await get().loadProducts()\n  },\n}))\n\n// Initialize the store\nuseProductStore.getState().loadProducts()\nuseProductStore.getState().loadCategories()\n"], "names": [], "mappings": ";;;AAAA;AAGA;AACA;;;;AA+BA,6BAA6B;AAC7B,MAAM,oBAAoB,IAAI,iKAAA,CAAA,wBAAqB;AACnD,MAAM,qBAAqB,IAAI,2JAAA,CAAA,qBAAkB,CAAC;AAE3C,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAgB,CAAC,KAAK,MAAQ,CAAC;QACjE,gBAAgB;QAChB,UAAU,EAAE;QACZ,SAAS;QACT,OAAO;QACP,SAAS,CAAC;QACV,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,YAAY,EAAE;QACd,aAAa;QACb,UAAU;QACV,YAAY;QACZ,aAAa;QACb,iBAAiB;QAEjB,UAAU;QACV,cAAc;YACZ,MAAM,QAAQ;YACd,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YAEjC,IAAI;gBACF,MAAM,WAAgC,MAAM,mBAAmB,OAAO,CAAC;oBACrE,SAAS;wBACP,GAAG,MAAM,OAAO;wBAChB,YAAY,MAAM,UAAU,IAAI;oBAClC;oBACA,QAAQ,MAAM,MAAM,IAAI;oBACxB,WAAW,MAAM,SAAS;oBAC1B,MAAM,MAAM,WAAW;oBACvB,UAAU,MAAM,QAAQ;gBAC1B;gBAEA,IAAI;oBACF,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;oBAC/B,aAAa,SAAS,WAAW;oBACjC,iBAAiB,SAAS,eAAe;oBACzC,SAAS;oBACT,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,UAAU,EAAE;gBACd;YACF;QACF;QAEA,gBAAgB;YACd,IAAI;gBACF,MAAM,aAAa,MAAM,kBAAkB,aAAa;gBACxD,IAAI;oBAAE;gBAAW;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;QAEA,YAAY,CAAC;YACX,MAAM,QAAQ;YACd,IAAI;gBACF,SAAS;oBAAE,GAAG,MAAM,OAAO;oBAAE,GAAG,UAAU;gBAAC;gBAC3C,aAAa;YACf;YACA,2CAA2C;YAC3C,MAAM,YAAY;QACpB;QAEA,YAAY,CAAC,QAAgB;YAC3B,IAAI;gBACF,QAAQ;gBACR;gBACA,aAAa;YACf;YACA,4CAA4C;YAC5C,MAAM,YAAY;QACpB;QAEA,eAAe,CAAC;YACd,IAAI;gBACF;gBACA,aAAa;YACf;YACA,2CAA2C;YAC3C,MAAM,YAAY;QACpB;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE,aAAa;YAAK;YACxB,yCAAyC;YACzC,MAAM,YAAY;QACpB;QAEA,cAAc;YACZ,IAAI;gBACF,SAAS,CAAC;gBACV,YAAY;gBACZ,QAAQ;gBACR,WAAW;gBACX,aAAa;YACf;YACA,gDAAgD;YAChD,MAAM,YAAY;QACpB;QAEA,iBAAiB;YACf,MAAM,MAAM,YAAY;QAC1B;IACF,CAAC;AAED,uBAAuB;AACvB,gBAAgB,QAAQ,GAAG,YAAY;AACvC,gBAAgB,QAAQ,GAAG,cAAc", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/domain/entities/CartItem.ts"], "sourcesContent": ["import { Product } from './Product'\nimport { Price } from '../value-objects/Price'\n\nexport interface CartItemProps {\n  product: Product\n  quantity: number\n  addedAt: Date\n}\n\nexport class CartItem {\n  private constructor(private props: CartItemProps) {\n    this.validateProps(props)\n  }\n\n  static create(product: Product, quantity: number = 1): CartItem {\n    return new CartItem({\n      product,\n      quantity,\n      addedAt: new Date(),\n    })\n  }\n\n  static fromJson(data: any, product: Product): CartItem {\n    return new CartItem({\n      product,\n      quantity: data.quantity,\n      addedAt: new Date(data.addedAt),\n    })\n  }\n\n  private validateProps(props: CartItemProps): void {\n    if (!props.product) {\n      throw new Error('Product is required')\n    }\n    if (props.quantity <= 0) {\n      throw new Error('Quantity must be greater than 0')\n    }\n    if (!props.addedAt) {\n      throw new Error('Added date is required')\n    }\n  }\n\n  get product(): Product {\n    return this.props.product\n  }\n\n  get quantity(): number {\n    return this.props.quantity\n  }\n\n  get addedAt(): Date {\n    return this.props.addedAt\n  }\n\n  get totalPrice(): Price {\n    return this.props.product.price.multiply(this.props.quantity)\n  }\n\n  get formattedTotalPrice(): string {\n    return this.totalPrice.format()\n  }\n\n  updateQuantity(newQuantity: number): CartItem {\n    if (newQuantity <= 0) {\n      throw new Error('Quantity must be greater than 0')\n    }\n    return new CartItem({\n      ...this.props,\n      quantity: newQuantity,\n    })\n  }\n\n  incrementQuantity(): CartItem {\n    return this.updateQuantity(this.props.quantity + 1)\n  }\n\n  decrementQuantity(): CartItem {\n    if (this.props.quantity <= 1) {\n      throw new Error('Cannot decrement quantity below 1')\n    }\n    return this.updateQuantity(this.props.quantity - 1)\n  }\n\n  equals(other: CartItem): boolean {\n    return this.props.product.equals(other.props.product)\n  }\n\n  toJson(): any {\n    return {\n      productId: this.props.product.id,\n      quantity: this.props.quantity,\n      addedAt: this.props.addedAt.toISOString(),\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AASO,MAAM;;IACX,YAAoB,AAAQ,KAAoB,CAAE;aAAtB,QAAA;QAC1B,IAAI,CAAC,aAAa,CAAC;IACrB;IAEA,OAAO,OAAO,OAAgB,EAAE,WAAmB,CAAC,EAAY;QAC9D,OAAO,IAAI,SAAS;YAClB;YACA;YACA,SAAS,IAAI;QACf;IACF;IAEA,OAAO,SAAS,IAAS,EAAE,OAAgB,EAAY;QACrD,OAAO,IAAI,SAAS;YAClB;YACA,UAAU,KAAK,QAAQ;YACvB,SAAS,IAAI,KAAK,KAAK,OAAO;QAChC;IACF;IAEQ,cAAc,KAAoB,EAAQ;QAChD,IAAI,CAAC,MAAM,OAAO,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,MAAM,QAAQ,IAAI,GAAG;YACvB,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,MAAM,OAAO,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,IAAI,UAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;IAC3B;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAEA,IAAI,UAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;IAC3B;IAEA,IAAI,aAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC9D;IAEA,IAAI,sBAA8B;QAChC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;IAC/B;IAEA,eAAe,WAAmB,EAAY;QAC5C,IAAI,eAAe,GAAG;YACpB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,SAAS;YAClB,GAAG,IAAI,CAAC,KAAK;YACb,UAAU;QACZ;IACF;IAEA,oBAA8B;QAC5B,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACnD;IAEA,oBAA8B;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,GAAG;YAC5B,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACnD;IAEA,OAAO,KAAe,EAAW;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,OAAO;IACtD;IAEA,SAAc;QACZ,OAAO;YACL,WAAW,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YAChC,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/domain/services/CartService.ts"], "sourcesContent": ["import { CartItem } from '../entities/CartItem'\nimport { Product } from '../entities/Product'\nimport { Price } from '../value-objects/Price'\n\nexport interface CartState {\n  items: CartItem[]\n  totalItems: number\n  totalPrice: Price\n  lastUpdated: Date\n}\n\nexport class CartService {\n  static createEmptyCart(): CartState {\n    return {\n      items: [],\n      totalItems: 0,\n      totalPrice: Price.create(0, 'INR'),\n      lastUpdated: new Date(),\n    }\n  }\n\n  static addItem(cart: CartState, product: Product, quantity: number = 1): CartState {\n    const existingItemIndex = cart.items.findIndex(item => \n      item.product.equals(product)\n    )\n\n    let newItems: CartItem[]\n\n    if (existingItemIndex >= 0) {\n      // Update existing item quantity\n      const existingItem = cart.items[existingItemIndex]\n      const updatedItem = existingItem.updateQuantity(existingItem.quantity + quantity)\n      newItems = [\n        ...cart.items.slice(0, existingItemIndex),\n        updatedItem,\n        ...cart.items.slice(existingItemIndex + 1),\n      ]\n    } else {\n      // Add new item\n      const newItem = CartItem.create(product, quantity)\n      newItems = [...cart.items, newItem]\n    }\n\n    return this.recalculateCart(newItems)\n  }\n\n  static removeItem(cart: CartState, productId: string): CartState {\n    const newItems = cart.items.filter(item => item.product.id !== productId)\n    return this.recalculateCart(newItems)\n  }\n\n  static updateItemQuantity(cart: CartState, productId: string, quantity: number): CartState {\n    if (quantity <= 0) {\n      return this.removeItem(cart, productId)\n    }\n\n    const itemIndex = cart.items.findIndex(item => item.product.id === productId)\n    if (itemIndex === -1) {\n      throw new Error(`Product with ID ${productId} not found in cart`)\n    }\n\n    const updatedItem = cart.items[itemIndex].updateQuantity(quantity)\n    const newItems = [\n      ...cart.items.slice(0, itemIndex),\n      updatedItem,\n      ...cart.items.slice(itemIndex + 1),\n    ]\n\n    return this.recalculateCart(newItems)\n  }\n\n  static clearCart(): CartState {\n    return this.createEmptyCart()\n  }\n\n  static getItemCount(cart: CartState): number {\n    return cart.totalItems\n  }\n\n  static getTotalPrice(cart: CartState): Price {\n    return cart.totalPrice\n  }\n\n  static hasItem(cart: CartState, productId: string): boolean {\n    return cart.items.some(item => item.product.id === productId)\n  }\n\n  static getItem(cart: CartState, productId: string): CartItem | null {\n    return cart.items.find(item => item.product.id === productId) || null\n  }\n\n  private static recalculateCart(items: CartItem[]): CartState {\n    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)\n    \n    let totalPrice = Price.create(0, 'INR')\n    if (items.length > 0) {\n      totalPrice = items.reduce((sum, item) => sum.add(item.totalPrice), Price.create(0, 'INR'))\n    }\n\n    return {\n      items,\n      totalItems,\n      totalPrice,\n      lastUpdated: new Date(),\n    }\n  }\n\n  static validateCart(cart: CartState): boolean {\n    try {\n      // Check if all items are valid\n      for (const item of cart.items) {\n        if (!item.product || item.quantity <= 0) {\n          return false\n        }\n      }\n\n      // Recalculate and verify totals\n      const recalculated = this.recalculateCart(cart.items)\n      return (\n        recalculated.totalItems === cart.totalItems &&\n        recalculated.totalPrice.equals(cart.totalPrice)\n      )\n    } catch {\n      return false\n    }\n  }\n\n  static toJson(cart: CartState): any {\n    return {\n      items: cart.items.map(item => item.toJson()),\n      totalItems: cart.totalItems,\n      totalPrice: cart.totalPrice.amount,\n      currency: cart.totalPrice.currency,\n      lastUpdated: cart.lastUpdated.toISOString(),\n    }\n  }\n\n  static fromJson(data: any, products: Product[]): CartState {\n    const items = data.items.map((itemData: any) => {\n      const product = products.find(p => p.id === itemData.productId)\n      if (!product) {\n        throw new Error(`Product with ID ${itemData.productId} not found`)\n      }\n      return CartItem.fromJson(itemData, product)\n    })\n\n    return this.recalculateCart(items)\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AASO,MAAM;IACX,OAAO,kBAA6B;QAClC,OAAO;YACL,OAAO,EAAE;YACT,YAAY;YACZ,YAAY,6IAAA,CAAA,QAAK,CAAC,MAAM,CAAC,GAAG;YAC5B,aAAa,IAAI;QACnB;IACF;IAEA,OAAO,QAAQ,IAAe,EAAE,OAAgB,EAAE,WAAmB,CAAC,EAAa;QACjF,MAAM,oBAAoB,KAAK,KAAK,CAAC,SAAS,CAAC,CAAA,OAC7C,KAAK,OAAO,CAAC,MAAM,CAAC;QAGtB,IAAI;QAEJ,IAAI,qBAAqB,GAAG;YAC1B,gCAAgC;YAChC,MAAM,eAAe,KAAK,KAAK,CAAC,kBAAkB;YAClD,MAAM,cAAc,aAAa,cAAc,CAAC,aAAa,QAAQ,GAAG;YACxE,WAAW;mBACN,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG;gBACvB;mBACG,KAAK,KAAK,CAAC,KAAK,CAAC,oBAAoB;aACzC;QACH,OAAO;YACL,eAAe;YACf,MAAM,UAAU,wIAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,SAAS;YACzC,WAAW;mBAAI,KAAK,KAAK;gBAAE;aAAQ;QACrC;QAEA,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B;IAEA,OAAO,WAAW,IAAe,EAAE,SAAiB,EAAa;QAC/D,MAAM,WAAW,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK;QAC/D,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B;IAEA,OAAO,mBAAmB,IAAe,EAAE,SAAiB,EAAE,QAAgB,EAAa;QACzF,IAAI,YAAY,GAAG;YACjB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;QAC/B;QAEA,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,UAAU,kBAAkB,CAAC;QAClE;QAEA,MAAM,cAAc,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC;QACzD,MAAM,WAAW;eACZ,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG;YACvB;eACG,KAAK,KAAK,CAAC,KAAK,CAAC,YAAY;SACjC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B;IAEA,OAAO,YAAuB;QAC5B,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA,OAAO,aAAa,IAAe,EAAU;QAC3C,OAAO,KAAK,UAAU;IACxB;IAEA,OAAO,cAAc,IAAe,EAAS;QAC3C,OAAO,KAAK,UAAU;IACxB;IAEA,OAAO,QAAQ,IAAe,EAAE,SAAiB,EAAW;QAC1D,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK;IACrD;IAEA,OAAO,QAAQ,IAAe,EAAE,SAAiB,EAAmB;QAClE,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,cAAc;IACnE;IAEA,OAAe,gBAAgB,KAAiB,EAAa;QAC3D,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;QAEpE,IAAI,aAAa,6IAAA,CAAA,QAAK,CAAC,MAAM,CAAC,GAAG;QACjC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,IAAI,GAAG,CAAC,KAAK,UAAU,GAAG,6IAAA,CAAA,QAAK,CAAC,MAAM,CAAC,GAAG;QACrF;QAEA,OAAO;YACL;YACA;YACA;YACA,aAAa,IAAI;QACnB;IACF;IAEA,OAAO,aAAa,IAAe,EAAW;QAC5C,IAAI;YACF,+BAA+B;YAC/B,KAAK,MAAM,QAAQ,KAAK,KAAK,CAAE;gBAC7B,IAAI,CAAC,KAAK,OAAO,IAAI,KAAK,QAAQ,IAAI,GAAG;oBACvC,OAAO;gBACT;YACF;YAEA,gCAAgC;YAChC,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC,KAAK,KAAK;YACpD,OACE,aAAa,UAAU,KAAK,KAAK,UAAU,IAC3C,aAAa,UAAU,CAAC,MAAM,CAAC,KAAK,UAAU;QAElD,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,OAAO,OAAO,IAAe,EAAO;QAClC,OAAO;YACL,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;YACzC,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,UAAU,CAAC,MAAM;YAClC,UAAU,KAAK,UAAU,CAAC,QAAQ;YAClC,aAAa,KAAK,WAAW,CAAC,WAAW;QAC3C;IACF;IAEA,OAAO,SAAS,IAAS,EAAE,QAAmB,EAAa;QACzD,MAAM,QAAQ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,SAAS;YAC9D,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,SAAS,SAAS,CAAC,UAAU,CAAC;YACnE;YACA,OAAO,wIAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,UAAU;QACrC;QAEA,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/stores/useCartStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { CartState, CartService } from '../domain/services/CartService'\nimport { Product } from '../domain/entities/Product'\n\ninterface CartStore extends CartState {\n  // Actions\n  addItem: (product: Product, quantity?: number) => void\n  removeItem: (productId: string) => void\n  updateItemQuantity: (productId: string, quantity: number) => void\n  clearCart: () => void\n  \n  // Computed values\n  getItemCount: () => number\n  getTotalPrice: () => string\n  hasItem: (productId: string) => boolean\n  getItemQuantity: (productId: string) => number\n}\n\nexport const useCartStore = create<CartStore>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      ...CartService.createEmptyCart(),\n\n      // Actions\n      addItem: (product: Product, quantity = 1) => {\n        const currentState = get()\n        const currentCart: CartState = {\n          items: currentState.items,\n          totalItems: currentState.totalItems,\n          totalPrice: currentState.totalPrice,\n          lastUpdated: currentState.lastUpdated,\n        }\n        \n        try {\n          const updatedCart = CartService.addItem(currentCart, product, quantity)\n          set(updatedCart)\n        } catch (error) {\n          console.error('Failed to add item to cart:', error)\n          throw error\n        }\n      },\n\n      removeItem: (productId: string) => {\n        const currentState = get()\n        const currentCart: CartState = {\n          items: currentState.items,\n          totalItems: currentState.totalItems,\n          totalPrice: currentState.totalPrice,\n          lastUpdated: currentState.lastUpdated,\n        }\n        \n        try {\n          const updatedCart = CartService.removeItem(currentCart, productId)\n          set(updatedCart)\n        } catch (error) {\n          console.error('Failed to remove item from cart:', error)\n          throw error\n        }\n      },\n\n      updateItemQuantity: (productId: string, quantity: number) => {\n        const currentState = get()\n        const currentCart: CartState = {\n          items: currentState.items,\n          totalItems: currentState.totalItems,\n          totalPrice: currentState.totalPrice,\n          lastUpdated: currentState.lastUpdated,\n        }\n        \n        try {\n          const updatedCart = CartService.updateItemQuantity(currentCart, productId, quantity)\n          set(updatedCart)\n        } catch (error) {\n          console.error('Failed to update item quantity:', error)\n          throw error\n        }\n      },\n\n      clearCart: () => {\n        const emptyCart = CartService.createEmptyCart()\n        set(emptyCart)\n      },\n\n      // Computed values\n      getItemCount: () => {\n        const state = get()\n        return CartService.getItemCount(state)\n      },\n\n      getTotalPrice: () => {\n        const state = get()\n        return CartService.getTotalPrice(state).format()\n      },\n\n      hasItem: (productId: string) => {\n        const state = get()\n        return CartService.hasItem(state, productId)\n      },\n\n      getItemQuantity: (productId: string) => {\n        const state = get()\n        const item = CartService.getItem(state, productId)\n        return item?.quantity || 0\n      },\n    }),\n    {\n      name: 'cart-storage',\n      partialize: (state) => ({\n        items: state.items.map(item => item.toJson()),\n        totalItems: state.totalItems,\n        totalPrice: {\n          amount: state.totalPrice.amount,\n          currency: state.totalPrice.currency,\n        },\n        lastUpdated: state.lastUpdated,\n      }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAiBO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,GAAG,2IAAA,CAAA,cAAW,CAAC,eAAe,EAAE;QAEhC,UAAU;QACV,SAAS,CAAC,SAAkB,WAAW,CAAC;YACtC,MAAM,eAAe;YACrB,MAAM,cAAyB;gBAC7B,OAAO,aAAa,KAAK;gBACzB,YAAY,aAAa,UAAU;gBACnC,YAAY,aAAa,UAAU;gBACnC,aAAa,aAAa,WAAW;YACvC;YAEA,IAAI;gBACF,MAAM,cAAc,2IAAA,CAAA,cAAW,CAAC,OAAO,CAAC,aAAa,SAAS;gBAC9D,IAAI;YACN,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM;YACR;QACF;QAEA,YAAY,CAAC;YACX,MAAM,eAAe;YACrB,MAAM,cAAyB;gBAC7B,OAAO,aAAa,KAAK;gBACzB,YAAY,aAAa,UAAU;gBACnC,YAAY,aAAa,UAAU;gBACnC,aAAa,aAAa,WAAW;YACvC;YAEA,IAAI;gBACF,MAAM,cAAc,2IAAA,CAAA,cAAW,CAAC,UAAU,CAAC,aAAa;gBACxD,IAAI;YACN,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM;YACR;QACF;QAEA,oBAAoB,CAAC,WAAmB;YACtC,MAAM,eAAe;YACrB,MAAM,cAAyB;gBAC7B,OAAO,aAAa,KAAK;gBACzB,YAAY,aAAa,UAAU;gBACnC,YAAY,aAAa,UAAU;gBACnC,aAAa,aAAa,WAAW;YACvC;YAEA,IAAI;gBACF,MAAM,cAAc,2IAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC,aAAa,WAAW;gBAC3E,IAAI;YACN,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,MAAM;YACR;QACF;QAEA,WAAW;YACT,MAAM,YAAY,2IAAA,CAAA,cAAW,CAAC,eAAe;YAC7C,IAAI;QACN;QAEA,kBAAkB;QAClB,cAAc;YACZ,MAAM,QAAQ;YACd,OAAO,2IAAA,CAAA,cAAW,CAAC,YAAY,CAAC;QAClC;QAEA,eAAe;YACb,MAAM,QAAQ;YACd,OAAO,2IAAA,CAAA,cAAW,CAAC,aAAa,CAAC,OAAO,MAAM;QAChD;QAEA,SAAS,CAAC;YACR,MAAM,QAAQ;YACd,OAAO,2IAAA,CAAA,cAAW,CAAC,OAAO,CAAC,OAAO;QACpC;QAEA,iBAAiB,CAAC;YAChB,MAAM,QAAQ;YACd,MAAM,OAAO,2IAAA,CAAA,cAAW,CAAC,OAAO,CAAC,OAAO;YACxC,OAAO,MAAM,YAAY;QAC3B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;YAC1C,YAAY,MAAM,UAAU;YAC5B,YAAY;gBACV,QAAQ,MAAM,UAAU,CAAC,MAAM;gBAC/B,UAAU,MAAM,UAAU,CAAC,QAAQ;YACrC;YACA,aAAa,MAAM,WAAW;QAChC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/UI/Button.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { clsx } from 'clsx'\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  fullWidth?: boolean\n}\n\nexport const Button = React.memo<ButtonProps>(({\n  children,\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  leftIcon,\n  rightIcon,\n  fullWidth = false,\n  className,\n  disabled,\n  ...props\n}) => {\n  const baseClasses = [\n    'inline-flex items-center justify-center font-medium transition-colors',\n    'focus:outline-none focus:ring-2 focus:ring-offset-2',\n    'disabled:opacity-50 disabled:cursor-not-allowed',\n    'rounded-lg',\n  ]\n\n  const variantClasses = {\n    primary: [\n      'bg-blue-600 text-white hover:bg-blue-700',\n      'focus:ring-blue-500',\n      'active:bg-blue-800',\n    ],\n    secondary: [\n      'bg-gray-600 text-white hover:bg-gray-700',\n      'focus:ring-gray-500',\n      'active:bg-gray-800',\n    ],\n    outline: [\n      'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50',\n      'focus:ring-blue-500',\n      'active:bg-gray-100',\n    ],\n    ghost: [\n      'text-gray-700 hover:bg-gray-100',\n      'focus:ring-gray-500',\n      'active:bg-gray-200',\n    ],\n    danger: [\n      'bg-red-600 text-white hover:bg-red-700',\n      'focus:ring-red-500',\n      'active:bg-red-800',\n    ],\n  }\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n  }\n\n  const classes = clsx(\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    fullWidth && 'w-full',\n    className\n  )\n\n  return (\n    <button\n      className={classes}\n      disabled={disabled || loading}\n      aria-disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"mr-2 h-4 w-4 animate-spin\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n          aria-hidden=\"true\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {!loading && leftIcon && <span className=\"mr-2\">{leftIcon}</span>}\n      {children}\n      {!loading && rightIcon && <span className=\"ml-2\">{rightIcon}</span>}\n    </button>\n  )\n})\n\nButton.displayName = 'Button'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcO,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,IAAI,MAAc,CAAC,EAC7C,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,QAAQ,EACR,SAAS,EACT,YAAY,KAAK,EACjB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;QAClB;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,SAAS;YACP;YACA;YACA;SACD;QACD,WAAW;YACT;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;SACD;QACD,OAAO;YACL;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;YACA;SACD;IACH;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACjB,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,aAAa,UACb;IAGF,qBACE,6LAAC;QACC,WAAW;QACX,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,eAAY;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,WAAW,0BAAY,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD;YACA,CAAC,WAAW,2BAAa,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;;AAEA,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/ProductCard/ProductCard.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport Image from 'next/image'\nimport { motion } from 'framer-motion'\nimport { clsx } from 'clsx'\nimport { Product } from '../../domain/entities/Product'\nimport { useCartStore } from '../../stores/useCartStore'\nimport { useSuccessToast, useErrorToast } from '../Toast/ToastContainer'\nimport { Button } from '../UI/Button'\n\ninterface ProductCardProps {\n  product: Product\n  className?: string\n}\n\nexport const ProductCard = React.memo<ProductCardProps>(({ product, className }) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [imageError, setImageError] = useState(false)\n  \n  const { addItem, hasItem, getItemQuantity, updateItemQuantity } = useCartStore()\n  const showSuccessToast = useSuccessToast()\n  const showErrorToast = useErrorToast()\n\n  const isInCart = hasItem(product.id)\n  const currentQuantity = getItemQuantity(product.id)\n\n  const handleAddToCart = useCallback(async () => {\n    if (!product.isAvailable) {\n      showErrorToast('This product is currently unavailable')\n      return\n    }\n\n    setIsLoading(true)\n    try {\n      addItem(product, 1)\n      showSuccessToast(`Added ${product.name} to cart`, {\n        duration: 3000,\n        action: {\n          label: 'View Cart',\n          onClick: () => {\n            // Navigate to cart - would implement with router\n            console.log('Navigate to cart')\n          }\n        }\n      })\n    } catch (error) {\n      showErrorToast(error instanceof Error ? error.message : 'Failed to add item to cart')\n    } finally {\n      setIsLoading(false)\n    }\n  }, [product, addItem, showSuccessToast, showErrorToast])\n\n  const handleQuantityChange = useCallback((newQuantity: number) => {\n    try {\n      updateItemQuantity(product.id, newQuantity)\n      showSuccessToast(`Updated ${product.name} quantity to ${newQuantity}`)\n    } catch (error) {\n      showErrorToast(error instanceof Error ? error.message : 'Failed to update quantity')\n    }\n  }, [product.id, product.name, updateItemQuantity, showSuccessToast, showErrorToast])\n\n  const handleImageError = useCallback(() => {\n    setImageError(true)\n  }, [])\n\n  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault()\n      handleAddToCart()\n    }\n  }, [handleAddToCart])\n\n  return (\n    <motion.article\n      className={clsx(\n        'group relative bg-white rounded-lg shadow-sm border border-gray-200',\n        'hover:shadow-md transition-shadow duration-200',\n        'focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2',\n        className\n      )}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      role=\"article\"\n      aria-labelledby={`product-${product.id}-name`}\n    >\n      {/* Product Image */}\n      <div className=\"relative aspect-square overflow-hidden rounded-t-lg bg-gray-100\">\n        {!imageError ? (\n          <Image\n            src={product.imageUrl}\n            alt={`${product.name} - ${product.description}`}\n            fill\n            className=\"object-cover group-hover:scale-105 transition-transform duration-200\"\n            onError={handleImageError}\n            sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n          />\n        ) : (\n          <div className=\"flex items-center justify-center h-full bg-gray-200\">\n            <svg\n              className=\"h-12 w-12 text-gray-400\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n              />\n            </svg>\n          </div>\n        )}\n        \n        {/* Availability Badge */}\n        {!product.isAvailable && (\n          <div className=\"absolute top-2 left-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded\">\n            Unavailable\n          </div>\n        )}\n      </div>\n\n      {/* Product Info */}\n      <div className=\"p-4\">\n        <div className=\"flex items-start justify-between mb-2\">\n          <h3\n            id={`product-${product.id}-name`}\n            className=\"text-lg font-semibold text-gray-900 line-clamp-2\"\n          >\n            {product.name}\n          </h3>\n          <button\n            type=\"button\"\n            className=\"ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors\"\n            aria-label={`Add ${product.name} to favorites`}\n          >\n            <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n              />\n            </svg>\n          </button>\n        </div>\n\n        <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n          {product.description}\n        </p>\n\n        {/* Rating and Reviews */}\n        <div className=\"flex items-center mb-3\">\n          <div className=\"flex items-center\">\n            {[...Array(5)].map((_, i) => (\n              <svg\n                key={i}\n                className={clsx(\n                  'h-4 w-4',\n                  i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'\n                )}\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n                aria-hidden=\"true\"\n              >\n                <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n              </svg>\n            ))}\n          </div>\n          <span className=\"ml-2 text-sm text-gray-500\">\n            {product.rating} ({product.reviewCount} reviews)\n          </span>\n        </div>\n\n        {/* Duration and Category */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n            {product.category}\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            ⏱️ {product.duration}\n          </span>\n        </div>\n\n        {/* Price and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <span className=\"text-2xl font-bold text-gray-900\">\n              {product.formattedPrice}\n            </span>\n          </div>\n\n          {!isInCart ? (\n            <Button\n              onClick={handleAddToCart}\n              onKeyDown={handleKeyDown}\n              loading={isLoading}\n              disabled={!product.isAvailable}\n              size=\"sm\"\n              aria-label={`Add ${product.name} to cart for ${product.formattedPrice}`}\n            >\n              Add to Cart\n            </Button>\n          ) : (\n            <div className=\"flex items-center space-x-2\">\n              <button\n                type=\"button\"\n                onClick={() => handleQuantityChange(currentQuantity - 1)}\n                className=\"w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center\"\n                aria-label=\"Decrease quantity\"\n                disabled={currentQuantity <= 1}\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 12H4\" />\n                </svg>\n              </button>\n              \n              <span\n                className=\"w-8 text-center font-medium\"\n                aria-label={`Current quantity: ${currentQuantity}`}\n              >\n                {currentQuantity}\n              </span>\n              \n              <button\n                type=\"button\"\n                onClick={() => handleQuantityChange(currentQuantity + 1)}\n                className=\"w-8 h-8 rounded-full bg-blue-100 hover:bg-blue-200 flex items-center justify-center\"\n                aria-label=\"Increase quantity\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                </svg>\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </motion.article>\n  )\n})\n\nProductCard.displayName = 'ProductCard'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AATA;;;;;;;;AAgBO,MAAM,4BAAc,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAmB,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE;;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAC7E,MAAM,mBAAmB,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD;IACvC,MAAM,iBAAiB,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD;IAEnC,MAAM,WAAW,QAAQ,QAAQ,EAAE;IACnC,MAAM,kBAAkB,gBAAgB,QAAQ,EAAE;IAElD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAClC,IAAI,CAAC,QAAQ,WAAW,EAAE;gBACxB,eAAe;gBACf;YACF;YAEA,aAAa;YACb,IAAI;gBACF,QAAQ,SAAS;gBACjB,iBAAiB,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAChD,UAAU;oBACV,QAAQ;wBACN,OAAO;wBACP,OAAO;wEAAE;gCACP,iDAAiD;gCACjD,QAAQ,GAAG,CAAC;4BACd;;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC1D,SAAU;gBACR,aAAa;YACf;QACF;mDAAG;QAAC;QAAS;QAAS;QAAkB;KAAe;IAEvD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACxC,IAAI;gBACF,mBAAmB,QAAQ,EAAE,EAAE;gBAC/B,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC,aAAa,EAAE,aAAa;YACvE,EAAE,OAAO,OAAO;gBACd,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC1D;QACF;wDAAG;QAAC,QAAQ,EAAE;QAAE,QAAQ,IAAI;QAAE;QAAoB;QAAkB;KAAe;IAEnF,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YACnC,cAAc;QAChB;oDAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACjC,IAAI,MAAM,GAAG,KAAK,WAAW,MAAM,GAAG,KAAK,KAAK;gBAC9C,MAAM,cAAc;gBACpB;YACF;QACF;iDAAG;QAAC;KAAgB;IAEpB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,uEACA,kDACA,6EACA;QAEF,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,MAAK;QACL,mBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;;0BAG7C,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,2BACA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,QAAQ;wBACrB,KAAK,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,QAAQ,WAAW,EAAE;wBAC/C,IAAI;wBACJ,WAAU;wBACV,SAAS;wBACT,OAAM;;;;;6CAGR,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,eAAY;sCAEZ,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;oBAOT,CAAC,QAAQ,WAAW,kBACnB,6LAAC;wBAAI,WAAU;kCAAsF;;;;;;;;;;;;0BAOzG,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;gCAChC,WAAU;0CAET,QAAQ,IAAI;;;;;;0CAEf,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,cAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC,aAAa,CAAC;0CAE9C,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAMV,6LAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;kCAItB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAEC,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,WACA,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,IAAI,oBAAoB;wCAEvD,MAAK;wCACL,SAAQ;wCACR,eAAY;kDAEZ,cAAA,6LAAC;4CAAK,GAAE;;;;;;uCATH;;;;;;;;;;0CAaX,6LAAC;gCAAK,WAAU;;oCACb,QAAQ,MAAM;oCAAC;oCAAG,QAAQ,WAAW;oCAAC;;;;;;;;;;;;;kCAK3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,QAAQ,QAAQ;;;;;;0CAEnB,6LAAC;gCAAK,WAAU;;oCAAwB;oCAClC,QAAQ,QAAQ;;;;;;;;;;;;;kCAKxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CACC,cAAA,6LAAC;oCAAK,WAAU;8CACb,QAAQ,cAAc;;;;;;;;;;;4BAI1B,CAAC,yBACA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAW;gCACX,SAAS;gCACT,UAAU,CAAC,QAAQ,WAAW;gCAC9B,MAAK;gCACL,cAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC,aAAa,EAAE,QAAQ,cAAc,EAAE;0CACxE;;;;;qDAID,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,qBAAqB,kBAAkB;wCACtD,WAAU;wCACV,cAAW;wCACX,UAAU,mBAAmB;kDAE7B,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAIzE,6LAAC;wCACC,WAAU;wCACV,cAAY,CAAC,kBAAkB,EAAE,iBAAiB;kDAEjD;;;;;;kDAGH,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,qBAAqB,kBAAkB;wCACtD,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvF;;QA/NoE,gIAAA,CAAA,eAAY;QACrD,gJAAA,CAAA,kBAAe;QACjB,gJAAA,CAAA,gBAAa;;;;QAF8B,gIAAA,CAAA,eAAY;QACrD,gJAAA,CAAA,kBAAe;QACjB,gJAAA,CAAA,gBAAa;;;;AA+NtC,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/UI/Input.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { clsx } from 'clsx'\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helperText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  fullWidth?: boolean\n}\n\nexport const Input = React.memo<InputProps>(({\n  label,\n  error,\n  helperText,\n  leftIcon,\n  rightIcon,\n  fullWidth = false,\n  className,\n  id,\n  ...props\n}) => {\n  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n  const errorId = error ? `${inputId}-error` : undefined\n  const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n  const inputClasses = clsx(\n    'block w-full rounded-lg border border-gray-300 px-3 py-2',\n    'text-gray-900 placeholder-gray-500',\n    'focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500',\n    'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',\n    error && 'border-red-500 focus:border-red-500 focus:ring-red-500',\n    leftIcon && 'pl-10',\n    rightIcon && 'pr-10',\n    className\n  )\n\n  const containerClasses = clsx(\n    'relative',\n    fullWidth && 'w-full'\n  )\n\n  return (\n    <div className={containerClasses}>\n      {label && (\n        <label\n          htmlFor={inputId}\n          className=\"mb-1 block text-sm font-medium text-gray-700\"\n        >\n          {label}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        {leftIcon && (\n          <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n            <span className=\"text-gray-400\" aria-hidden=\"true\">\n              {leftIcon}\n            </span>\n          </div>\n        )}\n        \n        <input\n          id={inputId}\n          className={inputClasses}\n          aria-invalid={error ? 'true' : 'false'}\n          aria-describedby={clsx(errorId, helperTextId).trim() || undefined}\n          {...props}\n        />\n        \n        {rightIcon && (\n          <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n            <span className=\"text-gray-400\" aria-hidden=\"true\">\n              {rightIcon}\n            </span>\n          </div>\n        )}\n      </div>\n      \n      {error && (\n        <p\n          id={errorId}\n          className=\"mt-1 text-sm text-red-600\"\n          role=\"alert\"\n          aria-live=\"polite\"\n        >\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p\n          id={helperTextId}\n          className=\"mt-1 text-sm text-gray-500\"\n        >\n          {helperText}\n        </p>\n      )}\n    </div>\n  )\n})\n\nInput.displayName = 'Input'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcO,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,IAAI,MAAa,CAAC,EAC3C,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,SAAS,EACT,YAAY,KAAK,EACjB,SAAS,EACT,EAAE,EACF,GAAG,OACJ;IACC,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,UAAU,QAAQ,GAAG,QAAQ,MAAM,CAAC,GAAG;IAC7C,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACtB,4DACA,sCACA,6EACA,0EACA,SAAS,0DACT,YAAY,SACZ,aAAa,SACb;IAGF,MAAM,mBAAmB,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAC1B,YACA,aAAa;IAGf,qBACE,6LAAC;QAAI,WAAW;;YACb,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAIL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;4BAAgB,eAAY;sCACzC;;;;;;;;;;;kCAKP,6LAAC;wBACC,IAAI;wBACJ,WAAW;wBACX,gBAAc,QAAQ,SAAS;wBAC/B,oBAAkB,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,SAAS,cAAc,IAAI,MAAM;wBACvD,GAAG,KAAK;;;;;;oBAGV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;4BAAgB,eAAY;sCACzC;;;;;;;;;;;;;;;;;YAMR,uBACC,6LAAC;gBACC,IAAI;gBACJ,WAAU;gBACV,MAAK;gBACL,aAAU;0BAET;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBACC,IAAI;gBACJ,WAAU;0BAET;;;;;;;;;;;;AAKX;;AAEA,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/ProductList/ProductFilters.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { useProductStore } from '../../stores/useProductStore'\nimport { Input } from '../UI/Input'\nimport { Button } from '../UI/Button'\n\nexport const ProductFilters = React.memo(() => {\n  const {\n    filters,\n    searchTerm,\n    categories,\n    setFilters,\n    setSearchTerm,\n    clearFilters,\n  } = useProductStore()\n\n  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm)\n  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)\n\n  const handleSearchSubmit = useCallback((e: React.FormEvent) => {\n    e.preventDefault()\n    setSearchTerm(localSearchTerm)\n  }, [localSearchTerm, setSearchTerm])\n\n  const handleCategoryChange = useCallback((category: string) => {\n    setFilters({\n      category: filters.category === category ? undefined : category\n    })\n  }, [filters.category, setFilters])\n\n  const handlePriceRangeChange = useCallback((field: 'minPrice' | 'maxPrice', value: string) => {\n    const numValue = value === '' ? undefined : Number(value)\n    setFilters({ [field]: numValue })\n  }, [setFilters])\n\n  const handleRatingChange = useCallback((rating: number) => {\n    setFilters({\n      minRating: filters.minRating === rating ? undefined : rating\n    })\n  }, [filters.minRating, setFilters])\n\n  const handleClearFilters = useCallback(() => {\n    setLocalSearchTerm('')\n    clearFilters()\n  }, [clearFilters])\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Search */}\n      <form onSubmit={handleSearchSubmit} className=\"flex gap-2\">\n        <Input\n          type=\"text\"\n          placeholder=\"Search services...\"\n          value={localSearchTerm}\n          onChange={(e) => setLocalSearchTerm(e.target.value)}\n          leftIcon={\n            <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          }\n          className=\"flex-1\"\n          aria-label=\"Search for services\"\n        />\n        <Button type=\"submit\" size=\"md\">\n          Search\n        </Button>\n      </form>\n\n      {/* Advanced Filters Toggle */}\n      <div className=\"flex items-center justify-between\">\n        <button\n          type=\"button\"\n          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}\n          className=\"text-sm text-blue-600 hover:text-blue-800 font-medium\"\n          aria-expanded={showAdvancedFilters}\n          aria-controls=\"advanced-filters\"\n        >\n          {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters\n          <svg\n            className={`ml-1 h-4 w-4 inline-block transition-transform ${\n              showAdvancedFilters ? 'rotate-180' : ''\n            }`}\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </button>\n\n        {(filters.category || filters.minPrice || filters.maxPrice || filters.minRating || searchTerm) && (\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleClearFilters}\n            aria-label=\"Clear all filters\"\n          >\n            Clear Filters\n          </Button>\n        )}\n      </div>\n\n      {/* Advanced Filters */}\n      {showAdvancedFilters && (\n        <div\n          id=\"advanced-filters\"\n          className=\"space-y-4 p-4 bg-gray-50 rounded-lg border\"\n        >\n          {/* Categories */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Category\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {categories.map((category) => (\n                <button\n                  key={category}\n                  type=\"button\"\n                  onClick={() => handleCategoryChange(category)}\n                  className={`px-3 py-1 text-sm rounded-full border transition-colors ${\n                    filters.category === category\n                      ? 'bg-blue-100 border-blue-300 text-blue-800'\n                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n                  }`}\n                  aria-pressed={filters.category === category}\n                >\n                  {category}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Price Range */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Price Range (₹)\n            </label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Input\n                type=\"number\"\n                placeholder=\"Min price\"\n                value={filters.minPrice || ''}\n                onChange={(e) => handlePriceRangeChange('minPrice', e.target.value)}\n                min=\"0\"\n                aria-label=\"Minimum price\"\n              />\n              <Input\n                type=\"number\"\n                placeholder=\"Max price\"\n                value={filters.maxPrice || ''}\n                onChange={(e) => handlePriceRangeChange('maxPrice', e.target.value)}\n                min=\"0\"\n                aria-label=\"Maximum price\"\n              />\n            </div>\n          </div>\n\n          {/* Rating Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Minimum Rating\n            </label>\n            <div className=\"flex gap-2\">\n              {[1, 2, 3, 4, 5].map((rating) => (\n                <button\n                  key={rating}\n                  type=\"button\"\n                  onClick={() => handleRatingChange(rating)}\n                  className={`flex items-center px-3 py-1 text-sm rounded border transition-colors ${\n                    filters.minRating === rating\n                      ? 'bg-yellow-100 border-yellow-300 text-yellow-800'\n                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n                  }`}\n                  aria-pressed={filters.minRating === rating}\n                  aria-label={`Filter by minimum ${rating} star rating`}\n                >\n                  <span className=\"mr-1\">{rating}</span>\n                  <svg className=\"h-4 w-4 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                  </svg>\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n})\n\nProductFilters.displayName = 'ProductFilters'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOO,MAAM,+BAAiB,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAC;;IACvC,MAAM,EACJ,OAAO,EACP,UAAU,EACV,UAAU,EACV,UAAU,EACV,aAAa,EACb,YAAY,EACb,GAAG,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACtC,EAAE,cAAc;YAChB,cAAc;QAChB;yDAAG;QAAC;QAAiB;KAAc;IAEnC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YACxC,WAAW;gBACT,UAAU,QAAQ,QAAQ,KAAK,WAAW,YAAY;YACxD;QACF;2DAAG;QAAC,QAAQ,QAAQ;QAAE;KAAW;IAEjC,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC,OAAgC;YAC1E,MAAM,WAAW,UAAU,KAAK,YAAY,OAAO;YACnD,WAAW;gBAAE,CAAC,MAAM,EAAE;YAAS;QACjC;6DAAG;QAAC;KAAW;IAEf,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACtC,WAAW;gBACT,WAAW,QAAQ,SAAS,KAAK,SAAS,YAAY;YACxD;QACF;yDAAG;QAAC,QAAQ,SAAS;QAAE;KAAW;IAElC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,mBAAmB;YACnB;QACF;yDAAG;QAAC;KAAa;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAK,UAAU;gBAAoB,WAAU;;kCAC5C,6LAAC,oIAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wBAClD,wBACE,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;wBAGzE,WAAU;wBACV,cAAW;;;;;;kCAEb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,MAAK;kCAAK;;;;;;;;;;;;0BAMlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,uBAAuB,CAAC;wBACvC,WAAU;wBACV,iBAAe;wBACf,iBAAc;;4BAEb,sBAAsB,SAAS;4BAAO;0CACvC,6LAAC;gCACC,WAAW,CAAC,+CAA+C,EACzD,sBAAsB,eAAe,IACrC;gCACF,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAER,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;oBAIxE,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,IAAI,UAAU,mBAC3F,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,cAAW;kCACZ;;;;;;;;;;;;YAOJ,qCACC,6LAAC;gBACC,IAAG;gBACH,WAAU;;kCAGV,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wCAEC,MAAK;wCACL,SAAS,IAAM,qBAAqB;wCACpC,WAAW,CAAC,wDAAwD,EAClE,QAAQ,QAAQ,KAAK,WACjB,8CACA,2DACJ;wCACF,gBAAc,QAAQ,QAAQ,KAAK;kDAElC;uCAVI;;;;;;;;;;;;;;;;kCAiBb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,OAAO,QAAQ,QAAQ,IAAI;wCAC3B,UAAU,CAAC,IAAM,uBAAuB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAClE,KAAI;wCACJ,cAAW;;;;;;kDAEb,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,OAAO,QAAQ,QAAQ,IAAI;wCAC3B,UAAU,CAAC,IAAM,uBAAuB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAClE,KAAI;wCACJ,cAAW;;;;;;;;;;;;;;;;;;kCAMjB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,uBACpB,6LAAC;wCAEC,MAAK;wCACL,SAAS,IAAM,mBAAmB;wCAClC,WAAW,CAAC,qEAAqE,EAC/E,QAAQ,SAAS,KAAK,SAClB,oDACA,2DACJ;wCACF,gBAAc,QAAQ,SAAS,KAAK;wCACpC,cAAY,CAAC,kBAAkB,EAAE,OAAO,YAAY,CAAC;;0DAErD,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAe,SAAQ;0DACnE,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;uCAbL;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBvB;;QA9KM,mIAAA,CAAA,kBAAe;;;;QAAf,mIAAA,CAAA,kBAAe;;;;AAgLrB,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2112, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/ProductList/ProductSort.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useCallback } from 'react'\nimport { useProductStore } from '../../stores/useProductStore'\n\nexport const ProductSort = React.memo(() => {\n  const { sortBy, sortOrder, setSorting } = useProductStore()\n\n  const handleSortChange = useCallback((newSortBy: string) => {\n    if (sortBy === newSortBy) {\n      // Toggle sort order if same field\n      setSorting(newSortBy, sortOrder === 'asc' ? 'desc' : 'asc')\n    } else {\n      // Set new field with default ascending order\n      setSorting(newSortBy, 'asc')\n    }\n  }, [sortBy, sortOrder, setSorting])\n\n  const sortOptions = [\n    { value: 'name', label: 'Name' },\n    { value: 'price', label: 'Price' },\n    { value: 'rating', label: 'Rating' },\n    { value: 'category', label: 'Category' },\n  ]\n\n  return (\n    <div className=\"flex items-center space-x-4\">\n      <span className=\"text-sm font-medium text-gray-700\">Sort by:</span>\n      \n      <div className=\"flex items-center space-x-2\">\n        {sortOptions.map((option) => (\n          <button\n            key={option.value}\n            type=\"button\"\n            onClick={() => handleSortChange(option.value)}\n            className={`inline-flex items-center px-3 py-1 text-sm font-medium rounded-md transition-colors ${\n              sortBy === option.value\n                ? 'bg-blue-100 text-blue-800 border border-blue-300'\n                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'\n            }`}\n            aria-pressed={sortBy === option.value}\n            aria-label={`Sort by ${option.label} ${\n              sortBy === option.value \n                ? `in ${sortOrder === 'asc' ? 'ascending' : 'descending'} order` \n                : ''\n            }`}\n          >\n            {option.label}\n            {sortBy === option.value && (\n              <svg\n                className={`ml-1 h-4 w-4 transition-transform ${\n                  sortOrder === 'desc' ? 'rotate-180' : ''\n                }`}\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n                aria-hidden=\"true\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M5 15l7-7 7 7\"\n                />\n              </svg>\n            )}\n          </button>\n        ))}\n      </div>\n    </div>\n  )\n})\n\nProductSort.displayName = 'ProductSort'\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,MAAM,4BAAc,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAC;;IACpC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IAExD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACpC,IAAI,WAAW,WAAW;gBACxB,kCAAkC;gBAClC,WAAW,WAAW,cAAc,QAAQ,SAAS;YACvD,OAAO;gBACL,6CAA6C;gBAC7C,WAAW,WAAW;YACxB;QACF;oDAAG;QAAC;QAAQ;QAAW;KAAW;IAElC,MAAM,cAAc;QAClB;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAY,OAAO;QAAW;KACxC;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;0BAAoC;;;;;;0BAEpD,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;wBAEC,MAAK;wBACL,SAAS,IAAM,iBAAiB,OAAO,KAAK;wBAC5C,WAAW,CAAC,oFAAoF,EAC9F,WAAW,OAAO,KAAK,GACnB,qDACA,kEACJ;wBACF,gBAAc,WAAW,OAAO,KAAK;wBACrC,cAAY,CAAC,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC,EACnC,WAAW,OAAO,KAAK,GACnB,CAAC,GAAG,EAAE,cAAc,QAAQ,cAAc,aAAa,MAAM,CAAC,GAC9D,IACJ;;4BAED,OAAO,KAAK;4BACZ,WAAW,OAAO,KAAK,kBACtB,6LAAC;gCACC,WAAW,CAAC,kCAAkC,EAC5C,cAAc,SAAS,eAAe,IACtC;gCACF,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,eAAY;0CAEZ,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;uBA9BH,OAAO,KAAK;;;;;;;;;;;;;;;;AAuC7B;;QAjE4C,mIAAA,CAAA,kBAAe;;;;QAAf,mIAAA,CAAA,kBAAe;;;;AAmE3D,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/UI/LoadingSpinner.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { clsx } from 'clsx'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  label?: string\n}\n\nexport const LoadingSpinner = React.memo<LoadingSpinnerProps>(({\n  size = 'md',\n  className,\n  label = 'Loading...',\n}) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n  }\n\n  return (\n    <div\n      className={clsx('flex items-center justify-center', className)}\n      role=\"status\"\n      aria-label={label}\n    >\n      <svg\n        className={clsx(\n          'animate-spin text-blue-600',\n          sizeClasses[size]\n        )}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        fill=\"none\"\n        viewBox=\"0 0 24 24\"\n        aria-hidden=\"true\"\n      >\n        <circle\n          className=\"opacity-25\"\n          cx=\"12\"\n          cy=\"12\"\n          r=\"10\"\n          stroke=\"currentColor\"\n          strokeWidth=\"4\"\n        />\n        <path\n          className=\"opacity-75\"\n          fill=\"currentColor\"\n          d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n        />\n      </svg>\n      <span className=\"sr-only\">{label}</span>\n    </div>\n  )\n})\n\nLoadingSpinner.displayName = 'LoadingSpinner'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,+BAAiB,6JAAA,CAAA,UAAK,CAAC,IAAI,MAAsB,CAAC,EAC7D,OAAO,IAAI,EACX,SAAS,EACT,QAAQ,YAAY,EACrB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,oCAAoC;QACpD,MAAK;QACL,cAAY;;0BAEZ,6LAAC;gBACC,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,8BACA,WAAW,CAAC,KAAK;gBAEnB,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,eAAY;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,<PERSON>E;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;0BAGN,6LAAC;gBAAK,WAAU;0BAAW;;;;;;;;;;;;AAGjC;;AAEA,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/UI/ErrorMessage.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { clsx } from 'clsx'\nimport { But<PERSON> } from './Button'\n\ninterface ErrorMessageProps {\n  message: string\n  onRetry?: () => void\n  className?: string\n  title?: string\n}\n\nexport const ErrorMessage = React.memo<ErrorMessageProps>(({\n  message,\n  onRetry,\n  className,\n  title = 'Something went wrong',\n}) => {\n  return (\n    <div\n      className={clsx(\n        'bg-red-50 border border-red-200 rounded-lg p-6 text-center',\n        className\n      )}\n      role=\"alert\"\n      aria-live=\"polite\"\n    >\n      <div className=\"flex justify-center mb-4\">\n        <svg\n          className=\"h-12 w-12 text-red-400\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          aria-hidden=\"true\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n          />\n        </svg>\n      </div>\n      \n      <h3 className=\"text-lg font-medium text-red-800 mb-2\">\n        {title}\n      </h3>\n      \n      <p className=\"text-red-700 mb-4\">\n        {message}\n      </p>\n      \n      {onRetry && (\n        <Button\n          onClick={onRetry}\n          variant=\"outline\"\n          size=\"sm\"\n        >\n          Try Again\n        </Button>\n      )}\n    </div>\n  )\n})\n\nErrorMessage.displayName = 'ErrorMessage'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaO,MAAM,6BAAe,6JAAA,CAAA,UAAK,CAAC,IAAI,MAAoB,CAAC,EACzD,OAAO,EACP,OAAO,EACP,SAAS,EACT,QAAQ,sBAAsB,EAC/B;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,8DACA;QAEF,MAAK;QACL,aAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;oBACR,eAAY;8BAEZ,cAAA,6LAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;0BAKR,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAGH,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAGF,yBACC,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS;gBACT,SAAQ;gBACR,MAAK;0BACN;;;;;;;;;;;;AAMT;;AAEA,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2418, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/ProductList/ProductList.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useMemo, useCallback } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { useProductStore } from '../../stores/useProductStore'\nimport { ProductCard } from '../ProductCard/ProductCard'\nimport { ProductFilters } from './ProductFilters'\nimport { ProductSort } from './ProductSort'\nimport { LoadingSpinner } from '../UI/LoadingSpinner'\nimport { ErrorMessage } from '../UI/ErrorMessage'\n\nexport const ProductList = React.memo(() => {\n  const {\n    products,\n    loading,\n    error,\n    totalCount,\n    currentPage,\n    hasNextPage,\n    hasPreviousPage,\n    refreshProducts,\n    setPage,\n  } = useProductStore()\n\n  const handleRetry = useCallback(() => {\n    refreshProducts()\n  }, [refreshProducts])\n\n  const handleNextPage = useCallback(() => {\n    if (hasNextPage) {\n      setPage(currentPage + 1)\n    }\n  }, [hasNextPage, currentPage, setPage])\n\n  const handlePreviousPage = useCallback(() => {\n    if (hasPreviousPage) {\n      setPage(currentPage - 1)\n    }\n  }, [hasPreviousPage, currentPage, setPage])\n\n  const memoizedProducts = useMemo(() => products, [products])\n\n  if (error) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <ErrorMessage\n          message={error}\n          onRetry={handleRetry}\n          className=\"max-w-md mx-auto\"\n        />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Fan Installation Services\n        </h1>\n        <p className=\"text-gray-600\">\n          Professional installation services for all types of fans\n        </p>\n      </div>\n\n      {/* Filters and Sort */}\n      <div className=\"mb-8 space-y-4 lg:space-y-0 lg:flex lg:items-center lg:justify-between\">\n        <ProductFilters />\n        <ProductSort />\n      </div>\n\n      {/* Results Count */}\n      <div className=\"mb-6\">\n        <p className=\"text-sm text-gray-600\">\n          {loading ? (\n            'Loading products...'\n          ) : (\n            `Showing ${products.length} of ${totalCount} services`\n          )}\n        </p>\n      </div>\n\n      {/* Loading State */}\n      {loading && (\n        <div className=\"flex justify-center py-12\">\n          <LoadingSpinner size=\"lg\" />\n        </div>\n      )}\n\n      {/* Products Grid */}\n      {!loading && (\n        <AnimatePresence mode=\"wait\">\n          {memoizedProducts.length > 0 ? (\n            <motion.div\n              key=\"products-grid\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\"\n            >\n              {memoizedProducts.map((product, index) => (\n                <motion.div\n                  key={product.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                >\n                  <ProductCard product={product} />\n                </motion.div>\n              ))}\n            </motion.div>\n          ) : (\n            <motion.div\n              key=\"no-products\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"text-center py-12\"\n            >\n              <svg\n                className=\"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n                aria-hidden=\"true\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20.4a7.962 7.962 0 01-5-1.691c-2.598-2.11-4.126-5.3-4.126-8.709 0-6.627 5.373-12 12-12s12 5.373 12 12c0 3.409-1.528 6.599-4.126 8.709A7.962 7.962 0 0112 20.4z\"\n                />\n              </svg>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                No services found\n              </h3>\n              <p className=\"text-gray-600\">\n                Try adjusting your filters or search terms\n              </p>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      )}\n\n      {/* Pagination */}\n      {!loading && memoizedProducts.length > 0 && (hasPreviousPage || hasNextPage) && (\n        <div className=\"mt-8 flex items-center justify-between\">\n          <button\n            type=\"button\"\n            onClick={handlePreviousPage}\n            disabled={!hasPreviousPage}\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n            aria-label=\"Go to previous page\"\n          >\n            <svg\n              className=\"mr-2 h-5 w-5\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M15 19l-7-7 7-7\"\n              />\n            </svg>\n            Previous\n          </button>\n\n          <span className=\"text-sm text-gray-700\">\n            Page {currentPage}\n          </span>\n\n          <button\n            type=\"button\"\n            onClick={handleNextPage}\n            disabled={!hasNextPage}\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n            aria-label=\"Go to next page\"\n          >\n            Next\n            <svg\n              className=\"ml-2 h-5 w-5\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M9 5l7 7-7 7\"\n              />\n            </svg>\n          </button>\n        </div>\n      )}\n    </div>\n  )\n})\n\nProductList.displayName = 'ProductList'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWO,MAAM,4BAAc,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAC;;IACpC,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EACL,UAAU,EACV,WAAW,EACX,WAAW,EACX,eAAe,EACf,eAAe,EACf,OAAO,EACR,GAAG,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC9B;QACF;+CAAG;QAAC;KAAgB;IAEpB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACjC,IAAI,aAAa;gBACf,QAAQ,cAAc;YACxB;QACF;kDAAG;QAAC;QAAa;QAAa;KAAQ;IAEtC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YACrC,IAAI,iBAAiB;gBACnB,QAAQ,cAAc;YACxB;QACF;sDAAG;QAAC;QAAiB;QAAa;KAAQ;IAE1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE,IAAM;gDAAU;QAAC;KAAS;IAE3D,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,2IAAA,CAAA,eAAY;gBACX,SAAS;gBACT,SAAS;gBACT,WAAU;;;;;;;;;;;IAIlB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,sJAAA,CAAA,iBAAc;;;;;kCACf,6LAAC,mJAAA,CAAA,cAAW;;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BACV,UACC,wBAEA,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,IAAI,EAAE,WAAW,SAAS,CAAC;;;;;;;;;;;YAM3D,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAA,CAAA,iBAAc;oBAAC,MAAK;;;;;;;;;;;YAKxB,CAAC,yBACA,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,iBAAiB,MAAM,GAAG,kBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAET,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,6LAAC,mJAAA,CAAA,cAAW;gCAAC,SAAS;;;;;;2BALjB,QAAQ,EAAE;;;;;mBATf;;;;yCAmBN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,eAAY;sCAEZ,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;sCAGN,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;mBAvBzB;;;;;;;;;;YAgCX,CAAC,WAAW,iBAAiB,MAAM,GAAG,KAAK,CAAC,mBAAmB,WAAW,mBACzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,WAAU;wBACV,cAAW;;0CAEX,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,eAAY;0CAEZ,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;4BAEA;;;;;;;kCAIR,6LAAC;wBAAK,WAAU;;4BAAwB;4BAChC;;;;;;;kCAGR,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,WAAU;wBACV,cAAW;;4BACZ;0CAEC,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,eAAY;0CAEZ,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB;;QAtLM,mIAAA,CAAA,kBAAe;;;;QAAf,mIAAA,CAAA,kBAAe;;;;AAwLrB,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2797, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/Cart/CartButton.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useMemo } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { useCartStore } from '../../stores/useCartStore'\nimport { Button } from '../UI/Button'\n\ninterface CartButtonProps {\n  className?: string\n  onClick?: () => void\n}\n\nexport const CartButton = React.memo<CartButtonProps>(({ className, onClick }) => {\n  const { totalItems, getTotalPrice } = useCartStore()\n\n  const formattedPrice = useMemo(() => getTotalPrice(), [getTotalPrice])\n\n  return (\n    <Button\n      onClick={onClick}\n      variant=\"primary\"\n      className={`relative ${className}`}\n      aria-label={`Shopping cart with ${totalItems} items, total ${formattedPrice}`}\n    >\n      <div className=\"flex items-center space-x-2\">\n        <div className=\"relative\">\n          <svg\n            className=\"h-6 w-6\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n            aria-hidden=\"true\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z\"\n            />\n          </svg>\n          \n          {/* Cart Count Badge */}\n          <AnimatePresence>\n            {totalItems > 0 && (\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                exit={{ scale: 0 }}\n                transition={{ type: 'spring', stiffness: 500, damping: 30 }}\n                className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center\"\n                aria-hidden=\"true\"\n              >\n                {totalItems > 99 ? '99+' : totalItems}\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n        \n        <div className=\"hidden sm:block\">\n          <div className=\"text-sm font-medium\">Cart</div>\n          {totalItems > 0 && (\n            <div className=\"text-xs opacity-90\">{formattedPrice}</div>\n          )}\n        </div>\n      </div>\n    </Button>\n  )\n})\n\nCartButton.displayName = 'CartButton'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAYO,MAAM,2BAAa,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAkB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE;;IAC3E,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAEjD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE,IAAM;6CAAiB;QAAC;KAAc;IAErE,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,SAAQ;QACR,WAAW,CAAC,SAAS,EAAE,WAAW;QAClC,cAAY,CAAC,mBAAmB,EAAE,WAAW,cAAc,EAAE,gBAAgB;kBAE7E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,eAAY;sCAEZ,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;sCAKN,6LAAC,4LAAA,CAAA,kBAAe;sCACb,aAAa,mBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,MAAM;oCAAE,OAAO;gCAAE;gCACjB,YAAY;oCAAE,MAAM;oCAAU,WAAW;oCAAK,SAAS;gCAAG;gCAC1D,WAAU;gCACV,eAAY;0CAEX,aAAa,KAAK,QAAQ;;;;;;;;;;;;;;;;;8BAMnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAsB;;;;;;wBACpC,aAAa,mBACZ,6LAAC;4BAAI,WAAU;sCAAsB;;;;;;;;;;;;;;;;;;;;;;;AAMjD;;QAtDwC,gIAAA,CAAA,eAAY;;;;QAAZ,gIAAA,CAAA,eAAY;;;;AAwDpD,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2947, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { ProductList } from '../components/ProductList/ProductList'\nimport { CartButton } from '../components/Cart/CartButton'\nimport { CartSidebar } from '../components/Cart/CartSidebar'\n\nexport default function Home() {\n  const [isCartOpen, setIsCartOpen] = useState(false)\n\n  const handleCartClick = () => {\n    setIsCartOpen(true)\n  }\n\n  const handleCartClose = () => {\n    setIsCartOpen(false)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                ServiceHub\n              </h1>\n              <nav className=\"hidden md:flex space-x-6\">\n                <a\n                  href=\"#\"\n                  className=\"text-gray-600 hover:text-gray-900 font-medium\"\n                >\n                  Services\n                </a>\n                <a\n                  href=\"#\"\n                  className=\"text-gray-600 hover:text-gray-900 font-medium\"\n                >\n                  About\n                </a>\n                <a\n                  href=\"#\"\n                  className=\"text-gray-600 hover:text-gray-900 font-medium\"\n                >\n                  Contact\n                </a>\n              </nav>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <CartButton onClick={handleCartClick} />\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main>\n        <ProductList />\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                ServiceHub\n              </h3>\n              <p className=\"text-gray-600 text-sm\">\n                Professional installation services for your home and office needs.\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-900 mb-3\">Services</h4>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Fan Installation</a></li>\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Electrical Work</a></li>\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Maintenance</a></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-900 mb-3\">Support</h4>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Help Center</a></li>\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Contact Us</a></li>\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Terms of Service</a></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-900 mb-3\">Connect</h4>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Twitter</a></li>\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Facebook</a></li>\n                <li><a href=\"#\" className=\"hover:text-gray-900\">Instagram</a></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-200 mt-8 pt-8 text-center text-sm text-gray-600\">\n            <p>&copy; 2024 ServiceHub. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAML,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2IAAA,CAAA,aAAU;oCAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,6LAAC;0BACC,cAAA,6LAAC,mJAAA,CAAA,cAAW;;;;;;;;;;0BAId,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;8DAChD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;8DAChD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;8CAIpD,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;8DAChD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;8DAChD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;8CAIpD,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;8DAChD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;8DAChD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GAvGwB;KAAA", "debugId": null}}]}