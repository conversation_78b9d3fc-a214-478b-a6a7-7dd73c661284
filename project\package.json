{"name": "product-catalog-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"clsx": "^2.1.1", "dompurify": "^3.1.7", "framer-motion": "^11.11.17", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^28.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}}