'use client'

import React, { useMemo, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useProductStore } from '../../stores/useProductStore'
import { ProductCard } from '../ProductCard/ProductCard'
import { ProductFilters } from './ProductFilters'
import { ProductSort } from './ProductSort'
import { LoadingSpinner } from '../UI/LoadingSpinner'
import { ErrorMessage } from '../UI/ErrorMessage'

export const ProductList = React.memo(() => {
  const {
    products,
    loading,
    error,
    totalCount,
    currentPage,
    hasNextPage,
    hasPreviousPage,
    refreshProducts,
    setPage,
  } = useProductStore()

  const handleRetry = useCallback(() => {
    refreshProducts()
  }, [refreshProducts])

  const handleNextPage = useCallback(() => {
    if (hasNextPage) {
      setPage(currentPage + 1)
    }
  }, [hasNextPage, currentPage, setPage])

  const handlePreviousPage = useCallback(() => {
    if (hasPreviousPage) {
      setPage(currentPage - 1)
    }
  }, [hasPreviousPage, currentPage, setPage])

  const memoizedProducts = useMemo(() => products, [products])

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <ErrorMessage
          message={error}
          onRetry={handleRetry}
          className="max-w-md mx-auto"
        />
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Fan Installation Services
        </h1>
        <p className="text-gray-600">
          Professional installation services for all types of fans
        </p>
      </div>

      {/* Filters and Sort */}
      <div className="mb-8 space-y-4 lg:space-y-0 lg:flex lg:items-center lg:justify-between">
        <ProductFilters />
        <ProductSort />
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          {loading ? (
            'Loading products...'
          ) : (
            `Showing ${products.length} of ${totalCount} services`
          )}
        </p>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      )}

      {/* Products Grid */}
      {!loading && (
        <AnimatePresence mode="wait">
          {memoizedProducts.length > 0 ? (
            <motion.div
              key="products-grid"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
              {memoizedProducts.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <ProductCard product={product} />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              key="no-products"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-12"
            >
              <svg
                className="mx-auto h-12 w-12 text-gray-400 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20.4a7.962 7.962 0 01-5-1.691c-2.598-2.11-4.126-5.3-4.126-8.709 0-6.627 5.373-12 12-12s12 5.373 12 12c0 3.409-1.528 6.599-4.126 8.709A7.962 7.962 0 0112 20.4z"
                />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No services found
              </h3>
              <p className="text-gray-600">
                Try adjusting your filters or search terms
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      )}

      {/* Pagination */}
      {!loading && memoizedProducts.length > 0 && (hasPreviousPage || hasNextPage) && (
        <div className="mt-8 flex items-center justify-between">
          <button
            type="button"
            onClick={handlePreviousPage}
            disabled={!hasPreviousPage}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Go to previous page"
          >
            <svg
              className="mr-2 h-5 w-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Previous
          </button>

          <span className="text-sm text-gray-700">
            Page {currentPage}
          </span>

          <button
            type="button"
            onClick={handleNextPage}
            disabled={!hasNextPage}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Go to next page"
          >
            Next
            <svg
              className="ml-2 h-5 w-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  )
})

ProductList.displayName = 'ProductList'
