'use client'

import React, { useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCartStore } from '../../stores/useCartStore'
import { Button } from '../UI/Button'

interface CartButtonProps {
  className?: string
  onClick?: () => void
}

export const CartButton = React.memo<CartButtonProps>(({ className, onClick }) => {
  const { totalItems, getTotalPrice } = useCartStore()

  const formattedPrice = useMemo(() => getTotalPrice(), [getTotalPrice])

  return (
    <Button
      onClick={onClick}
      variant="primary"
      className={`relative ${className}`}
      aria-label={`Shopping cart with ${totalItems} items, total ${formattedPrice}`}
    >
      <div className="flex items-center space-x-2">
        <div className="relative">
          <svg
            className="h-6 w-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"
            />
          </svg>
          
          {/* Cart Count Badge */}
          <AnimatePresence>
            {totalItems > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center"
                aria-hidden="true"
              >
                {totalItems > 99 ? '99+' : totalItems}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        
        <div className="hidden sm:block">
          <div className="text-sm font-medium">Cart</div>
          {totalItems > 0 && (
            <div className="text-xs opacity-90">{formattedPrice}</div>
          )}
        </div>
      </div>
    </Button>
  )
})

CartButton.displayName = 'CartButton'
