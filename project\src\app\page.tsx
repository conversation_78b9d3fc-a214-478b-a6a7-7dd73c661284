'use client'

import React, { useState } from 'react'
import { ProductList } from '../components/ProductList/ProductList'
import { CartButton } from '../components/Cart/CartButton'
import { CartSidebar } from '../components/Cart/CartSidebar'

export default function Home() {
  const [isCartOpen, setIsCartOpen] = useState(false)

  const handleCartClick = () => {
    setIsCartOpen(true)
  }

  const handleCartClose = () => {
    setIsCartOpen(false)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">
                ServiceHub
              </h1>
              <nav className="hidden md:flex space-x-6">
                <a
                  href="#"
                  className="text-gray-600 hover:text-gray-900 font-medium"
                >
                  Services
                </a>
                <a
                  href="#"
                  className="text-gray-600 hover:text-gray-900 font-medium"
                >
                  About
                </a>
                <a
                  href="#"
                  className="text-gray-600 hover:text-gray-900 font-medium"
                >
                  Contact
                </a>
              </nav>
            </div>

            <div className="flex items-center space-x-4">
              <CartButton onClick={handleCartClick} />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>
        <ProductList />
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                ServiceHub
              </h3>
              <p className="text-gray-600 text-sm">
                Professional installation services for your home and office needs.
              </p>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">Services</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-gray-900">Fan Installation</a></li>
                <li><a href="#" className="hover:text-gray-900">Electrical Work</a></li>
                <li><a href="#" className="hover:text-gray-900">Maintenance</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">Support</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-gray-900">Help Center</a></li>
                <li><a href="#" className="hover:text-gray-900">Contact Us</a></li>
                <li><a href="#" className="hover:text-gray-900">Terms of Service</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">Connect</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-gray-900">Twitter</a></li>
                <li><a href="#" className="hover:text-gray-900">Facebook</a></li>
                <li><a href="#" className="hover:text-gray-900">Instagram</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-200 mt-8 pt-8 text-center text-sm text-gray-600">
            <p>&copy; 2024 ServiceHub. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Cart Sidebar */}
      <CartSidebar isOpen={isCartOpen} onClose={handleCartClose} />
    </div>
  )
}
