import { Product } from '../../domain/entities/Product'
import { ProductRepository, ProductFilters } from '../../domain/repositories/ProductRepository'

export interface GetProductsRequest {
  filters?: ProductFilters
  sortBy?: 'name' | 'price' | 'rating' | 'category'
  sortOrder?: 'asc' | 'desc'
  page?: number
  pageSize?: number
}

export interface GetProductsResponse {
  products: Product[]
  totalCount: number
  page: number
  pageSize: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

export class GetProductsUseCase {
  constructor(private productRepository: ProductRepository) {}

  async execute(request: GetProductsRequest = {}): Promise<GetProductsResponse> {
    try {
      // Get all products with filters
      const allProducts = await this.productRepository.findAll(request.filters)

      // Apply sorting
      const sortedProducts = this.sortProducts(allProducts, request.sortBy, request.sortOrder)

      // Apply pagination
      const page = request.page || 1
      const pageSize = request.pageSize || 20
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize

      const paginatedProducts = sortedProducts.slice(startIndex, endIndex)

      return {
        products: paginatedProducts,
        totalCount: allProducts.length,
        page,
        pageSize,
        hasNextPage: endIndex < allProducts.length,
        hasPreviousPage: page > 1,
      }
    } catch (error) {
      throw new Error(`Failed to get products: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private sortProducts(
    products: Product[], 
    sortBy?: string, 
    sortOrder: 'asc' | 'desc' = 'asc'
  ): Product[] {
    if (!sortBy) return products

    const sorted = [...products].sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'price':
          comparison = a.price.amount - b.price.amount
          break
        case 'rating':
          comparison = a.rating - b.rating
          break
        case 'category':
          comparison = a.category.localeCompare(b.category)
          break
        default:
          return 0
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })

    return sorted
  }
}
