'use client'

import React, { createContext, useContext, useState, useCallback } from 'react'
import { Toast, ToastProps } from './Toast'

interface ToastContextType {
  showToast: (toast: Omit<ToastProps, 'id' | 'onClose'>) => void
  hideToast: (id: string) => void
  clearAllToasts: () => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export const useToast = () => {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

interface ToastProviderProps {
  children: React.ReactNode
  maxToasts?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
}

export const ToastProvider: React.FC<ToastProviderProps> = ({
  children,
  maxToasts = 5,
  position = 'top-right',
}) => {
  const [toasts, setToasts] = useState<(ToastProps & { id: string })[]>([])

  const showToast = useCallback((toast: Omit<ToastProps, 'id' | 'onClose'>) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newToast = {
      ...toast,
      id,
      onClose: (toastId: string) => hideToast(toastId),
    }

    setToasts(prevToasts => {
      const updatedToasts = [newToast, ...prevToasts]
      // Limit the number of toasts
      return updatedToasts.slice(0, maxToasts)
    })
  }, [maxToasts])

  const hideToast = useCallback((id: string) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id))
  }, [])

  const clearAllToasts = useCallback(() => {
    setToasts([])
  }, [])

  const positionClasses = {
    'top-right': 'top-0 right-0',
    'top-left': 'top-0 left-0',
    'bottom-right': 'bottom-0 right-0',
    'bottom-left': 'bottom-0 left-0',
    'top-center': 'top-0 left-1/2 transform -translate-x-1/2',
    'bottom-center': 'bottom-0 left-1/2 transform -translate-x-1/2',
  }

  return (
    <ToastContext.Provider value={{ showToast, hideToast, clearAllToasts }}>
      {children}
      
      {/* Toast Container */}
      <div
        className={`fixed z-50 p-6 pointer-events-none ${positionClasses[position]}`}
        aria-live="polite"
        aria-label="Notifications"
      >
        <div className="flex flex-col space-y-4">
          {toasts.map(toast => (
            <Toast key={toast.id} {...toast} />
          ))}
        </div>
      </div>
    </ToastContext.Provider>
  )
}

// Convenience hooks for different toast types
export const useSuccessToast = () => {
  const { showToast } = useToast()
  return useCallback((message: string, options?: Partial<ToastProps>) => {
    showToast({ ...options, message, type: 'success' })
  }, [showToast])
}

export const useErrorToast = () => {
  const { showToast } = useToast()
  return useCallback((message: string, options?: Partial<ToastProps>) => {
    showToast({ ...options, message, type: 'error' })
  }, [showToast])
}

export const useWarningToast = () => {
  const { showToast } = useToast()
  return useCallback((message: string, options?: Partial<ToastProps>) => {
    showToast({ ...options, message, type: 'warning' })
  }, [showToast])
}

export const useInfoToast = () => {
  const { showToast } = useToast()
  return useCallback((message: string, options?: Partial<ToastProps>) => {
    showToast({ ...options, message, type: 'info' })
  }, [showToast])
}
