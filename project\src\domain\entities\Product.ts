import { Price } from '../value-objects/Price'

export interface ProductProps {
  id: string
  name: string
  description: string
  price: Price
  imageUrl: string
  category: string
  rating: number
  reviewCount: number
  duration: string
  isAvailable: boolean
  tags?: string[]
}

export class Product {
  private constructor(private props: ProductProps) {
    this.validateProps(props)
  }

  static create(props: ProductProps): Product {
    return new Product(props)
  }

  static fromJson(data: any): Product {
    return new Product({
      id: data.id,
      name: data.name,
      description: data.description,
      price: Price.create(data.price, data.currency || 'INR'),
      imageUrl: data.imageUrl,
      category: data.category,
      rating: data.rating,
      reviewCount: data.reviewCount,
      duration: data.duration,
      isAvailable: data.isAvailable ?? true,
      tags: data.tags || [],
    })
  }

  private validateProps(props: ProductProps): void {
    if (!props.id || props.id.trim().length === 0) {
      throw new Error('Product ID is required')
    }
    if (!props.name || props.name.trim().length === 0) {
      throw new Error('Product name is required')
    }
    if (!props.description || props.description.trim().length === 0) {
      throw new Error('Product description is required')
    }
    if (!props.imageUrl || props.imageUrl.trim().length === 0) {
      throw new Error('Product image URL is required')
    }
    if (!props.category || props.category.trim().length === 0) {
      throw new Error('Product category is required')
    }
    if (props.rating < 0 || props.rating > 5) {
      throw new Error('Product rating must be between 0 and 5')
    }
    if (props.reviewCount < 0) {
      throw new Error('Product review count cannot be negative')
    }
  }

  get id(): string {
    return this.props.id
  }

  get name(): string {
    return this.props.name
  }

  get description(): string {
    return this.props.description
  }

  get price(): Price {
    return this.props.price
  }

  get imageUrl(): string {
    return this.props.imageUrl
  }

  get category(): string {
    return this.props.category
  }

  get rating(): number {
    return this.props.rating
  }

  get reviewCount(): number {
    return this.props.reviewCount
  }

  get duration(): string {
    return this.props.duration
  }

  get isAvailable(): boolean {
    return this.props.isAvailable
  }

  get tags(): string[] {
    return this.props.tags || []
  }

  get formattedPrice(): string {
    return this.props.price.format()
  }

  get formattedRating(): string {
    return `${this.props.rating} (${this.props.reviewCount} reviews)`
  }

  equals(other: Product): boolean {
    return this.props.id === other.props.id
  }

  toJson(): any {
    return {
      id: this.props.id,
      name: this.props.name,
      description: this.props.description,
      price: this.props.price.amount,
      currency: this.props.price.currency,
      imageUrl: this.props.imageUrl,
      category: this.props.category,
      rating: this.props.rating,
      reviewCount: this.props.reviewCount,
      duration: this.props.duration,
      isAvailable: this.props.isAvailable,
      tags: this.props.tags,
    }
  }
}
