import { CartItem } from '../entities/CartItem'
import { Product } from '../entities/Product'
import { Price } from '../value-objects/Price'

export interface CartState {
  items: CartItem[]
  totalItems: number
  totalPrice: Price
  lastUpdated: Date
}

export class CartService {
  static createEmptyCart(): CartState {
    return {
      items: [],
      totalItems: 0,
      totalPrice: Price.create(0, 'INR'),
      lastUpdated: new Date(),
    }
  }

  static addItem(cart: CartState, product: Product, quantity: number = 1): CartState {
    const existingItemIndex = cart.items.findIndex(item => 
      item.product.equals(product)
    )

    let newItems: CartItem[]

    if (existingItemIndex >= 0) {
      // Update existing item quantity
      const existingItem = cart.items[existingItemIndex]
      const updatedItem = existingItem.updateQuantity(existingItem.quantity + quantity)
      newItems = [
        ...cart.items.slice(0, existingItemIndex),
        updatedItem,
        ...cart.items.slice(existingItemIndex + 1),
      ]
    } else {
      // Add new item
      const newItem = CartItem.create(product, quantity)
      newItems = [...cart.items, newItem]
    }

    return this.recalculateCart(newItems)
  }

  static removeItem(cart: CartState, productId: string): CartState {
    const newItems = cart.items.filter(item => item.product.id !== productId)
    return this.recalculateCart(newItems)
  }

  static updateItemQuantity(cart: CartState, productId: string, quantity: number): CartState {
    if (quantity <= 0) {
      return this.removeItem(cart, productId)
    }

    const itemIndex = cart.items.findIndex(item => item.product.id === productId)
    if (itemIndex === -1) {
      throw new Error(`Product with ID ${productId} not found in cart`)
    }

    const updatedItem = cart.items[itemIndex].updateQuantity(quantity)
    const newItems = [
      ...cart.items.slice(0, itemIndex),
      updatedItem,
      ...cart.items.slice(itemIndex + 1),
    ]

    return this.recalculateCart(newItems)
  }

  static clearCart(): CartState {
    return this.createEmptyCart()
  }

  static getItemCount(cart: CartState): number {
    return cart.totalItems
  }

  static getTotalPrice(cart: CartState): Price {
    return cart.totalPrice
  }

  static hasItem(cart: CartState, productId: string): boolean {
    return cart.items.some(item => item.product.id === productId)
  }

  static getItem(cart: CartState, productId: string): CartItem | null {
    return cart.items.find(item => item.product.id === productId) || null
  }

  private static recalculateCart(items: CartItem[]): CartState {
    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
    
    let totalPrice = Price.create(0, 'INR')
    if (items.length > 0) {
      totalPrice = items.reduce((sum, item) => sum.add(item.totalPrice), Price.create(0, 'INR'))
    }

    return {
      items,
      totalItems,
      totalPrice,
      lastUpdated: new Date(),
    }
  }

  static validateCart(cart: CartState): boolean {
    try {
      // Check if all items are valid
      for (const item of cart.items) {
        if (!item.product || item.quantity <= 0) {
          return false
        }
      }

      // Recalculate and verify totals
      const recalculated = this.recalculateCart(cart.items)
      return (
        recalculated.totalItems === cart.totalItems &&
        recalculated.totalPrice.equals(cart.totalPrice)
      )
    } catch {
      return false
    }
  }

  static toJson(cart: CartState): any {
    return {
      items: cart.items.map(item => item.toJson()),
      totalItems: cart.totalItems,
      totalPrice: cart.totalPrice.amount,
      currency: cart.totalPrice.currency,
      lastUpdated: cart.lastUpdated.toISOString(),
    }
  }

  static fromJson(data: any, products: Product[]): CartState {
    const items = data.items.map((itemData: any) => {
      const product = products.find(p => p.id === itemData.productId)
      if (!product) {
        throw new Error(`Product with ID ${itemData.productId} not found`)
      }
      return CartItem.fromJson(itemData, product)
    })

    return this.recalculateCart(items)
  }
}
