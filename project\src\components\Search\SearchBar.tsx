'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { useProductStore } from '../../stores/useProductStore'
import { Input } from '../UI/Input'
import { Button } from '../UI/Button'

export const SearchBar = React.memo(() => {
  const { searchTerm, setSearchTerm } = useProductStore()
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm)

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearchTerm !== searchTerm) {
        setSearchTerm(localSearchTerm)
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [localSearchTerm, searchTerm, setSearchTerm])

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    setSearchTerm(localSearchTerm)
  }, [localSearchTerm, setSearchTerm])

  const handleClear = useCallback(() => {
    setLocalSearchTerm('')
    setSearchTerm('')
  }, [setSearchTerm])

  return (
    <form onSubmit={handleSubmit} className="flex gap-2 w-full max-w-lg">
      <div className="flex-1">
        <Input
          type="text"
          placeholder="Search for fan installation services..."
          value={localSearchTerm}
          onChange={(e) => setLocalSearchTerm(e.target.value)}
          leftIcon={
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          }
          rightIcon={
            localSearchTerm && (
              <button
                type="button"
                onClick={handleClear}
                className="text-gray-400 hover:text-gray-600"
                aria-label="Clear search"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )
          }
          aria-label="Search for services"
        />
      </div>
      
      <Button type="submit" size="md">
        Search
      </Button>
    </form>
  )
})

SearchBar.displayName = 'SearchBar'
