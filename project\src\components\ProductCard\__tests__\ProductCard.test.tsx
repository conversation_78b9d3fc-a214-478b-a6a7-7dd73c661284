import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ProductCard } from '../ProductCard'
import { Product } from '../../../domain/entities/Product'
import { Price } from '../../../domain/value-objects/Price'
import { ToastProvider } from '../../Toast/ToastContainer'

// Mock the stores
jest.mock('../../../stores/useCartStore', () => ({
  useCartStore: () => ({
    addItem: jest.fn(),
    hasItem: jest.fn(() => false),
    getItemQuantity: jest.fn(() => 0),
    updateItemQuantity: jest.fn(),
  }),
}))

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    article: ({ children, ...props }: any) => <article {...props}>{children}</article>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: any) {
    return <img src={src} alt={alt} {...props} />
  }
})

const mockProduct = Product.create({
  id: 'test-product-1',
  name: 'Test Fan Installation',
  description: 'Professional test fan installation service',
  price: Price.create(150, 'INR'),
  imageUrl: '/test-image.jpg',
  category: 'Installation',
  rating: 4.5,
  reviewCount: 10,
  duration: '30mins',
  isAvailable: true,
  tags: ['fan', 'installation'],
})

const renderWithToastProvider = (component: React.ReactElement) => {
  return render(
    <ToastProvider>
      {component}
    </ToastProvider>
  )
}

describe('ProductCard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders product information correctly', () => {
    renderWithToastProvider(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText('Test Fan Installation')).toBeInTheDocument()
    expect(screen.getByText('Professional test fan installation service')).toBeInTheDocument()
    expect(screen.getByText('₹150')).toBeInTheDocument()
    expect(screen.getByText('Installation')).toBeInTheDocument()
    expect(screen.getByText('⏱️ 30mins')).toBeInTheDocument()
    expect(screen.getByText('4.5 (10 reviews)')).toBeInTheDocument()
  })

  it('displays product image with correct alt text', () => {
    renderWithToastProvider(<ProductCard product={mockProduct} />)
    
    const image = screen.getByAltText('Test Fan Installation - Professional test fan installation service')
    expect(image).toBeInTheDocument()
    expect(image).toHaveAttribute('src', '/test-image.jpg')
  })

  it('shows Add to Cart button when product is not in cart', () => {
    renderWithToastProvider(<ProductCard product={mockProduct} />)
    
    const addButton = screen.getByRole('button', { name: /add test fan installation to cart/i })
    expect(addButton).toBeInTheDocument()
    expect(addButton).not.toBeDisabled()
  })

  it('disables Add to Cart button when product is unavailable', () => {
    const unavailableProduct = Product.create({
      ...mockProduct.toJson(),
      isAvailable: false,
    })
    
    renderWithToastProvider(<ProductCard product={unavailableProduct} />)
    
    const addButton = screen.getByRole('button', { name: /add test fan installation to cart/i })
    expect(addButton).toBeDisabled()
    expect(screen.getByText('Unavailable')).toBeInTheDocument()
  })

  it('handles add to cart click', async () => {
    const user = userEvent.setup()
    const mockAddItem = jest.fn()
    
    // Mock the store to return our mock function
    require('../../../stores/useCartStore').useCartStore.mockReturnValue({
      addItem: mockAddItem,
      hasItem: jest.fn(() => false),
      getItemQuantity: jest.fn(() => 0),
      updateItemQuantity: jest.fn(),
    })
    
    renderWithToastProvider(<ProductCard product={mockProduct} />)
    
    const addButton = screen.getByRole('button', { name: /add test fan installation to cart/i })
    await user.click(addButton)
    
    expect(mockAddItem).toHaveBeenCalledWith(mockProduct, 1)
  })

  it('shows quantity controls when product is in cart', () => {
    // Mock the store to show product is in cart
    require('../../../stores/useCartStore').useCartStore.mockReturnValue({
      addItem: jest.fn(),
      hasItem: jest.fn(() => true),
      getItemQuantity: jest.fn(() => 2),
      updateItemQuantity: jest.fn(),
    })
    
    renderWithToastProvider(<ProductCard product={mockProduct} />)
    
    expect(screen.getByLabelText('Decrease quantity')).toBeInTheDocument()
    expect(screen.getByLabelText('Increase quantity')).toBeInTheDocument()
    expect(screen.getByLabelText('Current quantity: 2')).toBeInTheDocument()
  })

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup()
    const mockAddItem = jest.fn()
    
    require('../../../stores/useCartStore').useCartStore.mockReturnValue({
      addItem: mockAddItem,
      hasItem: jest.fn(() => false),
      getItemQuantity: jest.fn(() => 0),
      updateItemQuantity: jest.fn(),
    })
    
    renderWithToastProvider(<ProductCard product={mockProduct} />)
    
    const addButton = screen.getByRole('button', { name: /add test fan installation to cart/i })
    addButton.focus()
    
    await user.keyboard('{Enter}')
    expect(mockAddItem).toHaveBeenCalledWith(mockProduct, 1)
  })

  it('displays rating stars correctly', () => {
    renderWithToastProvider(<ProductCard product={mockProduct} />)
    
    // Should have 4 filled stars (rating is 4.5, so 4 full stars)
    const stars = screen.getAllByRole('img', { hidden: true })
    // Note: This is a simplified test - in a real scenario you'd check the star colors
    expect(stars).toHaveLength(5) // 5 star elements total
  })

  it('has proper accessibility attributes', () => {
    renderWithToastProvider(<ProductCard product={mockProduct} />)
    
    const article = screen.getByRole('article')
    expect(article).toHaveAttribute('aria-labelledby', 'product-test-product-1-name')
    
    const productName = screen.getByText('Test Fan Installation')
    expect(productName).toHaveAttribute('id', 'product-test-product-1-name')
    
    const favoriteButton = screen.getByLabelText('Add Test Fan Installation to favorites')
    expect(favoriteButton).toBeInTheDocument()
  })
})
