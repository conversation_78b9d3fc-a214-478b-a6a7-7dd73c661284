'use client'

import React, { useCallback } from 'react'
import { useProductStore } from '../../stores/useProductStore'

export const ProductSort = React.memo(() => {
  const { sortBy, sortOrder, setSorting } = useProductStore()

  const handleSortChange = useCallback((newSortBy: string) => {
    if (sortBy === newSortBy) {
      // Toggle sort order if same field
      setSorting(newSortBy, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // Set new field with default ascending order
      setSorting(newSortBy, 'asc')
    }
  }, [sortBy, sortOrder, setSorting])

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'price', label: 'Price' },
    { value: 'rating', label: 'Rating' },
    { value: 'category', label: 'Category' },
  ]

  return (
    <div className="flex items-center space-x-4">
      <span className="text-sm font-medium text-gray-700">Sort by:</span>
      
      <div className="flex items-center space-x-2">
        {sortOptions.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={() => handleSortChange(option.value)}
            className={`inline-flex items-center px-3 py-1 text-sm font-medium rounded-md transition-colors ${
              sortBy === option.value
                ? 'bg-blue-100 text-blue-800 border border-blue-300'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
            aria-pressed={sortBy === option.value}
            aria-label={`Sort by ${option.label} ${
              sortBy === option.value 
                ? `in ${sortOrder === 'asc' ? 'ascending' : 'descending'} order` 
                : ''
            }`}
          >
            {option.label}
            {sortBy === option.value && (
              <svg
                className={`ml-1 h-4 w-4 transition-transform ${
                  sortOrder === 'desc' ? 'rotate-180' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 15l7-7 7 7"
                />
              </svg>
            )}
          </button>
        ))}
      </div>
    </div>
  )
})

ProductSort.displayName = 'ProductSort'
