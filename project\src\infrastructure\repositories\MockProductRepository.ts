import { Product } from '../../domain/entities/Product'
import { 
  ProductRepository, 
  ProductFilters,
  ProductNotFoundError,
  ProductRepositoryConnectionError,
  ProductRepositoryTimeoutError
} from '../../domain/repositories/ProductRepository'
import { CircuitBreaker } from '../api/CircuitBreaker'
import { RetryPolicy, defaultRetryConfig } from '../api/RetryPolicy'
import productsData from '../../data/products.json'

export class MockProductRepository implements ProductRepository {
  private products: Product[]
  private circuitBreaker: CircuitBreaker
  private retryPolicy: RetryPolicy
  private simulateFailure: boolean = false
  private failureRate: number = 0.1 // 10% failure rate for testing

  constructor() {
    this.products = productsData.map(data => Product.fromJson(data))
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 5,
      recoveryTimeout: 30000, // 30 seconds
      monitoringPeriod: 60000, // 1 minute
    })
    this.retryPolicy = new RetryPolicy(defaultRetryConfig)
  }

  async findAll(filters?: ProductFilters): Promise<Product[]> {
    return this.circuitBreaker.execute(async () => {
      return this.retryPolicy.execute(async () => {
        await this.simulateNetworkDelay()
        this.maybeThrowError()

        let filteredProducts = [...this.products]

        if (filters) {
          if (filters.category) {
            filteredProducts = filteredProducts.filter(p => 
              p.category.toLowerCase() === filters.category!.toLowerCase()
            )
          }

          if (filters.minPrice !== undefined) {
            filteredProducts = filteredProducts.filter(p => 
              p.price.amount >= filters.minPrice!
            )
          }

          if (filters.maxPrice !== undefined) {
            filteredProducts = filteredProducts.filter(p => 
              p.price.amount <= filters.maxPrice!
            )
          }

          if (filters.minRating !== undefined) {
            filteredProducts = filteredProducts.filter(p => 
              p.rating >= filters.minRating!
            )
          }

          if (filters.searchTerm) {
            const searchTerm = filters.searchTerm.toLowerCase()
            filteredProducts = filteredProducts.filter(p => 
              p.name.toLowerCase().includes(searchTerm) ||
              p.description.toLowerCase().includes(searchTerm) ||
              p.tags.some(tag => tag.toLowerCase().includes(searchTerm))
            )
          }

          if (filters.tags && filters.tags.length > 0) {
            filteredProducts = filteredProducts.filter(p => 
              filters.tags!.some(tag => 
                p.tags.some(productTag => 
                  productTag.toLowerCase() === tag.toLowerCase()
                )
              )
            )
          }
        }

        return filteredProducts
      })
    })
  }

  async findById(id: string): Promise<Product | null> {
    return this.circuitBreaker.execute(async () => {
      return this.retryPolicy.execute(async () => {
        await this.simulateNetworkDelay()
        this.maybeThrowError()

        const product = this.products.find(p => p.id === id)
        return product || null
      })
    })
  }

  async findByCategory(category: string): Promise<Product[]> {
    return this.findAll({ category })
  }

  async search(searchTerm: string): Promise<Product[]> {
    return this.findAll({ searchTerm })
  }

  async getCategories(): Promise<string[]> {
    return this.circuitBreaker.execute(async () => {
      return this.retryPolicy.execute(async () => {
        await this.simulateNetworkDelay()
        this.maybeThrowError()

        const categories = [...new Set(this.products.map(p => p.category))]
        return categories.sort()
      })
    })
  }

  // Testing utilities
  setSimulateFailure(simulate: boolean): void {
    this.simulateFailure = simulate
  }

  setFailureRate(rate: number): void {
    this.failureRate = Math.max(0, Math.min(1, rate))
  }

  resetCircuitBreaker(): void {
    this.circuitBreaker.reset()
  }

  getCircuitBreakerState(): string {
    return this.circuitBreaker.getState()
  }

  private async simulateNetworkDelay(): Promise<void> {
    // Simulate realistic network delay (50-200ms)
    const delay = Math.random() * 150 + 50
    await new Promise(resolve => setTimeout(resolve, delay))
  }

  private maybeThrowError(): void {
    if (this.simulateFailure || Math.random() < this.failureRate) {
      const errorTypes = [
        () => new ProductRepositoryConnectionError('Network connection failed'),
        () => new ProductRepositoryTimeoutError('Request timeout'),
        () => new Error('Internal server error'),
      ]
      
      const randomError = errorTypes[Math.floor(Math.random() * errorTypes.length)]
      throw randomError()
    }
  }
}
