import { Product } from '../Product'
import { Price } from '../../value-objects/Price'

describe('Product Entity', () => {
  const validProductProps = {
    id: 'test-product-1',
    name: 'Test Product',
    description: 'A test product description',
    price: Price.create(100, 'INR'),
    imageUrl: '/test-image.jpg',
    category: 'Test Category',
    rating: 4.5,
    reviewCount: 10,
    duration: '30mins',
    isAvailable: true,
    tags: ['test', 'product'],
  }

  describe('Product Creation', () => {
    it('should create a product with valid props', () => {
      const product = Product.create(validProductProps)
      
      expect(product.id).toBe('test-product-1')
      expect(product.name).toBe('Test Product')
      expect(product.description).toBe('A test product description')
      expect(product.price.amount).toBe(100)
      expect(product.imageUrl).toBe('/test-image.jpg')
      expect(product.category).toBe('Test Category')
      expect(product.rating).toBe(4.5)
      expect(product.reviewCount).toBe(10)
      expect(product.duration).toBe('30mins')
      expect(product.isAvailable).toBe(true)
      expect(product.tags).toEqual(['test', 'product'])
    })

    it('should create a product from JSON', () => {
      const jsonData = {
        id: 'json-product-1',
        name: 'JSON Product',
        description: 'A product from JSON',
        price: 150,
        currency: 'INR',
        imageUrl: '/json-image.jpg',
        category: 'JSON Category',
        rating: 4.0,
        reviewCount: 5,
        duration: '45mins',
        isAvailable: true,
        tags: ['json', 'test'],
      }

      const product = Product.fromJson(jsonData)
      
      expect(product.id).toBe('json-product-1')
      expect(product.name).toBe('JSON Product')
      expect(product.price.amount).toBe(150)
      expect(product.price.currency).toBe('INR')
    })
  })

  describe('Product Validation', () => {
    it('should throw error for empty ID', () => {
      expect(() => {
        Product.create({
          ...validProductProps,
          id: '',
        })
      }).toThrow('Product ID is required')
    })

    it('should throw error for empty name', () => {
      expect(() => {
        Product.create({
          ...validProductProps,
          name: '',
        })
      }).toThrow('Product name is required')
    })

    it('should throw error for invalid rating', () => {
      expect(() => {
        Product.create({
          ...validProductProps,
          rating: 6,
        })
      }).toThrow('Product rating must be between 0 and 5')
    })

    it('should throw error for negative review count', () => {
      expect(() => {
        Product.create({
          ...validProductProps,
          reviewCount: -1,
        })
      }).toThrow('Product review count cannot be negative')
    })
  })

  describe('Product Methods', () => {
    let product: Product

    beforeEach(() => {
      product = Product.create(validProductProps)
    })

    it('should format price correctly', () => {
      expect(product.formattedPrice).toBe('₹100')
    })

    it('should format rating correctly', () => {
      expect(product.formattedRating).toBe('4.5 (10 reviews)')
    })

    it('should check equality correctly', () => {
      const sameProduct = Product.create(validProductProps)
      const differentProduct = Product.create({
        ...validProductProps,
        id: 'different-id',
      })

      expect(product.equals(sameProduct)).toBe(true)
      expect(product.equals(differentProduct)).toBe(false)
    })

    it('should convert to JSON correctly', () => {
      const json = product.toJson()
      
      expect(json.id).toBe('test-product-1')
      expect(json.name).toBe('Test Product')
      expect(json.price).toBe(100)
      expect(json.currency).toBe('INR')
      expect(json.tags).toEqual(['test', 'product'])
    })
  })
})
