import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { CartState, CartService } from '../domain/services/CartService'
import { Product } from '../domain/entities/Product'

interface CartStore extends CartState {
  // Actions
  addItem: (product: Product, quantity?: number) => void
  removeItem: (productId: string) => void
  updateItemQuantity: (productId: string, quantity: number) => void
  clearCart: () => void
  
  // Computed values
  getItemCount: () => number
  getTotalPrice: () => string
  hasItem: (productId: string) => boolean
  getItemQuantity: (productId: string) => number
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      // Initial state
      ...CartService.createEmptyCart(),

      // Actions
      addItem: (product: Product, quantity = 1) => {
        const currentState = get()
        const currentCart: CartState = {
          items: currentState.items,
          totalItems: currentState.totalItems,
          totalPrice: currentState.totalPrice,
          lastUpdated: currentState.lastUpdated,
        }
        
        try {
          const updatedCart = CartService.addItem(currentCart, product, quantity)
          set(updatedCart)
        } catch (error) {
          console.error('Failed to add item to cart:', error)
          throw error
        }
      },

      removeItem: (productId: string) => {
        const currentState = get()
        const currentCart: CartState = {
          items: currentState.items,
          totalItems: currentState.totalItems,
          totalPrice: currentState.totalPrice,
          lastUpdated: currentState.lastUpdated,
        }
        
        try {
          const updatedCart = CartService.removeItem(currentCart, productId)
          set(updatedCart)
        } catch (error) {
          console.error('Failed to remove item from cart:', error)
          throw error
        }
      },

      updateItemQuantity: (productId: string, quantity: number) => {
        const currentState = get()
        const currentCart: CartState = {
          items: currentState.items,
          totalItems: currentState.totalItems,
          totalPrice: currentState.totalPrice,
          lastUpdated: currentState.lastUpdated,
        }
        
        try {
          const updatedCart = CartService.updateItemQuantity(currentCart, productId, quantity)
          set(updatedCart)
        } catch (error) {
          console.error('Failed to update item quantity:', error)
          throw error
        }
      },

      clearCart: () => {
        const emptyCart = CartService.createEmptyCart()
        set(emptyCart)
      },

      // Computed values
      getItemCount: () => {
        const state = get()
        return CartService.getItemCount(state)
      },

      getTotalPrice: () => {
        const state = get()
        return CartService.getTotalPrice(state).format()
      },

      hasItem: (productId: string) => {
        const state = get()
        return CartService.hasItem(state, productId)
      },

      getItemQuantity: (productId: string) => {
        const state = get()
        const item = CartService.getItem(state, productId)
        return item?.quantity || 0
      },
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({
        items: state.items.map(item => item.toJson()),
        totalItems: state.totalItems,
        totalPrice: {
          amount: state.totalPrice.amount,
          currency: state.totalPrice.currency,
        },
        lastUpdated: state.lastUpdated,
      }),
    }
  )
)
