import { Product } from '../../domain/entities/Product'
import { Price } from '../../domain/value-objects/Price'
import DOMPurify from 'dompurify'

export interface CreateProductRequest {
  id: string
  name: string
  description: string
  price: number
  currency?: string
  imageUrl: string
  category: string
  rating: number
  reviewCount: number
  duration: string
  isAvailable?: boolean
  tags?: string[]
}

export class ProductFactory {
  static create(request: CreateProductRequest): Product {
    // Sanitize inputs to prevent XSS
    const sanitizedRequest = this.sanitizeInputs(request)
    
    // Validate inputs
    this.validateInputs(sanitizedRequest)

    // Create price value object
    const price = Price.create(sanitizedRequest.price, sanitizedRequest.currency || 'INR')

    // Create product
    return Product.create({
      id: sanitizedRequest.id,
      name: sanitizedRequest.name,
      description: sanitizedRequest.description,
      price,
      imageUrl: sanitizedRequest.imageUrl,
      category: sanitizedRequest.category,
      rating: sanitizedRequest.rating,
      reviewCount: sanitizedRequest.reviewCount,
      duration: sanitizedRequest.duration,
      isAvailable: sanitizedRequest.isAvailable ?? true,
      tags: sanitizedRequest.tags || [],
    })
  }

  static createFromJson(data: any): Product {
    return this.create({
      id: data.id,
      name: data.name,
      description: data.description,
      price: data.price,
      currency: data.currency,
      imageUrl: data.imageUrl,
      category: data.category,
      rating: data.rating,
      reviewCount: data.reviewCount,
      duration: data.duration,
      isAvailable: data.isAvailable,
      tags: data.tags,
    })
  }

  static createBatch(requests: CreateProductRequest[]): Product[] {
    return requests.map(request => this.create(request))
  }

  private static sanitizeInputs(request: CreateProductRequest): CreateProductRequest {
    return {
      ...request,
      id: this.sanitizeString(request.id),
      name: this.sanitizeString(request.name),
      description: this.sanitizeString(request.description),
      imageUrl: this.sanitizeUrl(request.imageUrl),
      category: this.sanitizeString(request.category),
      duration: this.sanitizeString(request.duration),
      tags: request.tags?.map(tag => this.sanitizeString(tag)),
    }
  }

  private static sanitizeString(input: string): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string')
    }
    return DOMPurify.sanitize(input.trim(), { ALLOWED_TAGS: [] })
  }

  private static sanitizeUrl(url: string): string {
    if (typeof url !== 'string') {
      throw new Error('URL must be a string')
    }
    
    const sanitized = DOMPurify.sanitize(url.trim(), { ALLOWED_TAGS: [] })
    
    // Basic URL validation
    if (!sanitized.match(/^(https?:\/\/|\/)/)) {
      throw new Error('Invalid URL format')
    }
    
    return sanitized
  }

  private static validateInputs(request: CreateProductRequest): void {
    // ID validation
    if (!request.id || request.id.length === 0) {
      throw new Error('Product ID is required')
    }
    if (request.id.length > 100) {
      throw new Error('Product ID must be less than 100 characters')
    }
    if (!/^[a-zA-Z0-9-_]+$/.test(request.id)) {
      throw new Error('Product ID can only contain letters, numbers, hyphens, and underscores')
    }

    // Name validation
    if (!request.name || request.name.length === 0) {
      throw new Error('Product name is required')
    }
    if (request.name.length > 200) {
      throw new Error('Product name must be less than 200 characters')
    }

    // Description validation
    if (!request.description || request.description.length === 0) {
      throw new Error('Product description is required')
    }
    if (request.description.length > 1000) {
      throw new Error('Product description must be less than 1000 characters')
    }

    // Price validation
    if (typeof request.price !== 'number' || request.price < 0) {
      throw new Error('Product price must be a non-negative number')
    }
    if (request.price > 1000000) {
      throw new Error('Product price cannot exceed 1,000,000')
    }

    // Currency validation
    if (request.currency && !/^[A-Z]{3}$/.test(request.currency)) {
      throw new Error('Currency must be a 3-letter code')
    }

    // Category validation
    if (!request.category || request.category.length === 0) {
      throw new Error('Product category is required')
    }
    if (request.category.length > 100) {
      throw new Error('Product category must be less than 100 characters')
    }

    // Rating validation
    if (typeof request.rating !== 'number' || request.rating < 0 || request.rating > 5) {
      throw new Error('Product rating must be between 0 and 5')
    }

    // Review count validation
    if (typeof request.reviewCount !== 'number' || request.reviewCount < 0) {
      throw new Error('Product review count must be a non-negative number')
    }

    // Duration validation
    if (!request.duration || request.duration.length === 0) {
      throw new Error('Product duration is required')
    }
    if (request.duration.length > 50) {
      throw new Error('Product duration must be less than 50 characters')
    }

    // Tags validation
    if (request.tags) {
      if (request.tags.length > 20) {
        throw new Error('Product cannot have more than 20 tags')
      }
      for (const tag of request.tags) {
        if (typeof tag !== 'string' || tag.length === 0) {
          throw new Error('All tags must be non-empty strings')
        }
        if (tag.length > 50) {
          throw new Error('Each tag must be less than 50 characters')
        }
      }
    }
  }
}
