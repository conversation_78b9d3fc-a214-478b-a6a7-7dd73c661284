import { Product } from './Product'
import { Price } from '../value-objects/Price'

export interface CartItemProps {
  product: Product
  quantity: number
  addedAt: Date
}

export class CartItem {
  private constructor(private props: CartItemProps) {
    this.validateProps(props)
  }

  static create(product: Product, quantity: number = 1): CartItem {
    return new CartItem({
      product,
      quantity,
      addedAt: new Date(),
    })
  }

  static fromJson(data: any, product: Product): CartItem {
    return new CartItem({
      product,
      quantity: data.quantity,
      addedAt: new Date(data.addedAt),
    })
  }

  private validateProps(props: CartItemProps): void {
    if (!props.product) {
      throw new Error('Product is required')
    }
    if (props.quantity <= 0) {
      throw new Error('Quantity must be greater than 0')
    }
    if (!props.addedAt) {
      throw new Error('Added date is required')
    }
  }

  get product(): Product {
    return this.props.product
  }

  get quantity(): number {
    return this.props.quantity
  }

  get addedAt(): Date {
    return this.props.addedAt
  }

  get totalPrice(): Price {
    return this.props.product.price.multiply(this.props.quantity)
  }

  get formattedTotalPrice(): string {
    return this.totalPrice.format()
  }

  updateQuantity(newQuantity: number): CartItem {
    if (newQuantity <= 0) {
      throw new Error('Quantity must be greater than 0')
    }
    return new CartItem({
      ...this.props,
      quantity: newQuantity,
    })
  }

  incrementQuantity(): CartItem {
    return this.updateQuantity(this.props.quantity + 1)
  }

  decrementQuantity(): CartItem {
    if (this.props.quantity <= 1) {
      throw new Error('Cannot decrement quantity below 1')
    }
    return this.updateQuantity(this.props.quantity - 1)
  }

  equals(other: CartItem): boolean {
    return this.props.product.equals(other.props.product)
  }

  toJson(): any {
    return {
      productId: this.props.product.id,
      quantity: this.props.quantity,
      addedAt: this.props.addedAt.toISOString(),
    }
  }
}
