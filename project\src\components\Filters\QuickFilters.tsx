'use client'

import React, { useCallback } from 'react'
import { useProductStore } from '../../stores/useProductStore'
import { Button } from '../UI/Button'

export const QuickFilters = React.memo(() => {
  const { filters, categories, setFilters, clearFilters } = useProductStore()

  const handleCategoryFilter = useCallback((category: string) => {
    setFilters({
      category: filters.category === category ? undefined : category
    })
  }, [filters.category, setFilters])

  const handlePriceFilter = useCallback((minPrice: number, maxPrice: number) => {
    const isActive = filters.minPrice === minPrice && filters.maxPrice === maxPrice
    setFilters({
      minPrice: isActive ? undefined : minPrice,
      maxPrice: isActive ? undefined : maxPrice,
    })
  }, [filters.minPrice, filters.maxPrice, setFilters])

  const handleRatingFilter = useCallback((rating: number) => {
    setFilters({
      minRating: filters.minRating === rating ? undefined : rating
    })
  }, [filters.minRating, setFilters])

  const priceRanges = [
    { label: 'Under ₹100', min: 0, max: 100 },
    { label: '₹100 - ₹200', min: 100, max: 200 },
    { label: 'Over ₹200', min: 200, max: 1000 },
  ]

  const hasActiveFilters = filters.category || filters.minPrice || filters.maxPrice || filters.minRating

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Quick Filters</h3>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            aria-label="Clear all filters"
          >
            Clear All
          </Button>
        )}
      </div>

      <div className="space-y-4">
        {/* Categories */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Categories</h4>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => handleCategoryFilter(category)}
                className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                  filters.category === category
                    ? 'bg-blue-100 border-blue-300 text-blue-800'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
                aria-pressed={filters.category === category}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Price Ranges */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Price Range</h4>
          <div className="flex flex-wrap gap-2">
            {priceRanges.map((range) => {
              const isActive = filters.minPrice === range.min && filters.maxPrice === range.max
              return (
                <button
                  key={range.label}
                  onClick={() => handlePriceFilter(range.min, range.max)}
                  className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                    isActive
                      ? 'bg-green-100 border-green-300 text-green-800'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                  aria-pressed={isActive}
                >
                  {range.label}
                </button>
              )
            })}
          </div>
        </div>

        {/* Rating Filter */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Minimum Rating</h4>
          <div className="flex gap-2">
            {[4, 4.5, 5].map((rating) => (
              <button
                key={rating}
                onClick={() => handleRatingFilter(rating)}
                className={`flex items-center px-3 py-1 text-sm rounded border transition-colors ${
                  filters.minRating === rating
                    ? 'bg-yellow-100 border-yellow-300 text-yellow-800'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
                aria-pressed={filters.minRating === rating}
              >
                <span className="mr-1">{rating}</span>
                <svg className="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <span className="ml-1">+</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
})

QuickFilters.displayName = 'QuickFilters'
