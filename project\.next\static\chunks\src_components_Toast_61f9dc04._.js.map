{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/Toast/Toast.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { clsx } from 'clsx'\n\nexport interface ToastProps {\n  id: string\n  message: string\n  type?: 'success' | 'error' | 'warning' | 'info'\n  duration?: number\n  onClose: (id: string) => void\n  action?: {\n    label: string\n    onClick: () => void\n  }\n}\n\nexport const Toast = React.memo<ToastProps>(({\n  id,\n  message,\n  type = 'info',\n  duration = 5000,\n  onClose,\n  action,\n}) => {\n  const [isVisible, setIsVisible] = useState(true)\n\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(false)\n        setTimeout(() => onClose(id), 300) // Wait for exit animation\n      }, duration)\n\n      return () => clearTimeout(timer)\n    }\n  }, [duration, id, onClose])\n\n  const handleClose = () => {\n    setIsVisible(false)\n    setTimeout(() => onClose(id), 300)\n  }\n\n  const handleKeyDown = (event: React.KeyboardEvent) => {\n    if (event.key === 'Escape') {\n      handleClose()\n    }\n  }\n\n  const typeStyles = {\n    success: 'bg-green-50 border-green-200 text-green-800',\n    error: 'bg-red-50 border-red-200 text-red-800',\n    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n    info: 'bg-blue-50 border-blue-200 text-blue-800',\n  }\n\n  const iconStyles = {\n    success: 'text-green-400',\n    error: 'text-red-400',\n    warning: 'text-yellow-400',\n    info: 'text-blue-400',\n  }\n\n  const icons = {\n    success: (\n      <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\" aria-hidden=\"true\">\n        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n      </svg>\n    ),\n    error: (\n      <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\" aria-hidden=\"true\">\n        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n      </svg>\n    ),\n    warning: (\n      <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\" aria-hidden=\"true\">\n        <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n      </svg>\n    ),\n    info: (\n      <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\" aria-hidden=\"true\">\n        <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n      </svg>\n    ),\n  }\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 50, scale: 0.3 }}\n          animate={{ opacity: 1, y: 0, scale: 1 }}\n          exit={{ opacity: 0, y: 20, scale: 0.5 }}\n          transition={{ duration: 0.3, ease: 'easeOut' }}\n          className={clsx(\n            'pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg border shadow-lg',\n            typeStyles[type]\n          )}\n          role=\"alert\"\n          aria-live=\"assertive\"\n          aria-atomic=\"true\"\n          onKeyDown={handleKeyDown}\n          tabIndex={0}\n        >\n          <div className=\"p-4\">\n            <div className=\"flex items-start\">\n              <div className=\"flex-shrink-0\">\n                <span className={clsx('inline-block', iconStyles[type])}>\n                  {icons[type]}\n                </span>\n              </div>\n              <div className=\"ml-3 w-0 flex-1 pt-0.5\">\n                <p className=\"text-sm font-medium\">{message}</p>\n                {action && (\n                  <div className=\"mt-3 flex space-x-7\">\n                    <button\n                      type=\"button\"\n                      className=\"rounded-md text-sm font-medium hover:underline focus:outline-none focus:ring-2 focus:ring-offset-2\"\n                      onClick={action.onClick}\n                    >\n                      {action.label}\n                    </button>\n                  </div>\n                )}\n              </div>\n              <div className=\"ml-4 flex flex-shrink-0\">\n                <button\n                  type=\"button\"\n                  className=\"inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2\"\n                  onClick={handleClose}\n                  aria-label=\"Close notification\"\n                >\n                  <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\" aria-hidden=\"true\">\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n})\n\nToast.displayName = 'Toast'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAkBO,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAa,CAAC,EAC3C,EAAE,EACF,OAAO,EACP,OAAO,MAAM,EACb,WAAW,IAAI,EACf,OAAO,EACP,MAAM,EACP;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,WAAW,GAAG;gBAChB,MAAM,QAAQ;6CAAW;wBACvB,aAAa;wBACb;qDAAW,IAAM,QAAQ;oDAAK,KAAK,0BAA0B;;oBAC/D;4CAAG;gBAEH;uCAAO,IAAM,aAAa;;YAC5B;QACF;0BAAG;QAAC;QAAU;QAAI;KAAQ;IAE1B,MAAM,cAAc;QAClB,aAAa;QACb,WAAW,IAAM,QAAQ,KAAK;IAChC;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,MAAM,GAAG,KAAK,UAAU;YAC1B;QACF;IACF;IAEA,MAAM,aAAa;QACjB,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,aAAa;QACjB,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,uBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;YAAY,eAAY;sBAC3E,cAAA,6LAAC;gBAAK,UAAS;gBAAU,GAAE;gBAAwI,UAAS;;;;;;;;;;;QAGhL,qBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;YAAY,eAAY;sBAC3E,cAAA,6LAAC;gBAAK,UAAS;gBAAU,GAAE;gBAA0N,UAAS;;;;;;;;;;;QAGlQ,uBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;YAAY,eAAY;sBAC3E,cAAA,6LAAC;gBAAK,UAAS;gBAAU,GAAE;gBAAoN,UAAS;;;;;;;;;;;QAG5P,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;YAAY,eAAY;sBAC3E,cAAA,6LAAC;gBAAK,UAAS;gBAAU,GAAE;gBAAmI,UAAS;;;;;;;;;;;IAG7K;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAI;YACzC,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAG,OAAO;YAAE;YACtC,MAAM;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAI;YACtC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,mFACA,UAAU,CAAC,KAAK;YAElB,MAAK;YACL,aAAU;YACV,eAAY;YACZ,WAAW;YACX,UAAU;sBAEV,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,UAAU,CAAC,KAAK;0CACnD,KAAK,CAAC,KAAK;;;;;;;;;;;sCAGhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAuB;;;;;;gCACnC,wBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,OAAO,OAAO;kDAEtB,OAAO,KAAK;;;;;;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;0CAEX,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;oCAAY,eAAY;8CAC3E,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7P;;AAEA,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/src/components/Toast/ToastContainer.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useCallback } from 'react'\nimport { Toast, ToastProps } from './Toast'\n\ninterface ToastContextType {\n  showToast: (toast: Omit<ToastProps, 'id' | 'onClose'>) => void\n  hideToast: (id: string) => void\n  clearAllToasts: () => void\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined)\n\nexport const useToast = () => {\n  const context = useContext(ToastContext)\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider')\n  }\n  return context\n}\n\ninterface ToastProviderProps {\n  children: React.ReactNode\n  maxToasts?: number\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'\n}\n\nexport const ToastProvider: React.FC<ToastProviderProps> = ({\n  children,\n  maxToasts = 5,\n  position = 'top-right',\n}) => {\n  const [toasts, setToasts] = useState<(ToastProps & { id: string })[]>([])\n\n  const showToast = useCallback((toast: Omit<ToastProps, 'id' | 'onClose'>) => {\n    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n    const newToast = {\n      ...toast,\n      id,\n      onClose: (toastId: string) => hideToast(toastId),\n    }\n\n    setToasts(prevToasts => {\n      const updatedToasts = [newToast, ...prevToasts]\n      // Limit the number of toasts\n      return updatedToasts.slice(0, maxToasts)\n    })\n  }, [maxToasts])\n\n  const hideToast = useCallback((id: string) => {\n    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id))\n  }, [])\n\n  const clearAllToasts = useCallback(() => {\n    setToasts([])\n  }, [])\n\n  const positionClasses = {\n    'top-right': 'top-0 right-0',\n    'top-left': 'top-0 left-0',\n    'bottom-right': 'bottom-0 right-0',\n    'bottom-left': 'bottom-0 left-0',\n    'top-center': 'top-0 left-1/2 transform -translate-x-1/2',\n    'bottom-center': 'bottom-0 left-1/2 transform -translate-x-1/2',\n  }\n\n  return (\n    <ToastContext.Provider value={{ showToast, hideToast, clearAllToasts }}>\n      {children}\n      \n      {/* Toast Container */}\n      <div\n        className={`fixed z-50 p-6 pointer-events-none ${positionClasses[position]}`}\n        aria-live=\"polite\"\n        aria-label=\"Notifications\"\n      >\n        <div className=\"flex flex-col space-y-4\">\n          {toasts.map(toast => (\n            <Toast key={toast.id} {...toast} />\n          ))}\n        </div>\n      </div>\n    </ToastContext.Provider>\n  )\n}\n\n// Convenience hooks for different toast types\nexport const useSuccessToast = () => {\n  const { showToast } = useToast()\n  return useCallback((message: string, options?: Partial<ToastProps>) => {\n    showToast({ ...options, message, type: 'success' })\n  }, [showToast])\n}\n\nexport const useErrorToast = () => {\n  const { showToast } = useToast()\n  return useCallback((message: string, options?: Partial<ToastProps>) => {\n    showToast({ ...options, message, type: 'error' })\n  }, [showToast])\n}\n\nexport const useWarningToast = () => {\n  const { showToast } = useToast()\n  return useCallback((message: string, options?: Partial<ToastProps>) => {\n    showToast({ ...options, message, type: 'warning' })\n  }, [showToast])\n}\n\nexport const useInfoToast = () => {\n  const { showToast } = useToast()\n  return useCallback((message: string, options?: Partial<ToastProps>) => {\n    showToast({ ...options, message, type: 'info' })\n  }, [showToast])\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;AAHA;;;AAWA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAcN,MAAM,gBAA8C,CAAC,EAC1D,QAAQ,EACR,YAAY,CAAC,EACb,WAAW,WAAW,EACvB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC,EAAE;IAExE,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC7B,MAAM,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAC3E,MAAM,WAAW;gBACf,GAAG,KAAK;gBACR;gBACA,OAAO;4DAAE,CAAC,UAAoB,UAAU;;YAC1C;YAEA;wDAAU,CAAA;oBACR,MAAM,gBAAgB;wBAAC;2BAAa;qBAAW;oBAC/C,6BAA6B;oBAC7B,OAAO,cAAc,KAAK,CAAC,GAAG;gBAChC;;QACF;+CAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC7B;wDAAU,CAAA,aAAc,WAAW,MAAM;gEAAC,CAAA,QAAS,MAAM,EAAE,KAAK;;;QAClE;+CAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YACjC,UAAU,EAAE;QACd;oDAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,aAAa;QACb,YAAY;QACZ,gBAAgB;QAChB,eAAe;QACf,cAAc;QACd,iBAAiB;IACnB;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAW;YAAW;QAAe;;YAClE;0BAGD,6LAAC;gBACC,WAAW,CAAC,mCAAmC,EAAE,eAAe,CAAC,SAAS,EAAE;gBAC5E,aAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAA,sBACV,6LAAC,uIAAA,CAAA,QAAK;4BAAiB,GAAG,KAAK;2BAAnB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;AAMhC;IAzDa;KAAA;AA4DN,MAAM,kBAAkB;;IAC7B,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE,CAAC,SAAiB;YACnC,UAAU;gBAAE,GAAG,OAAO;gBAAE;gBAAS,MAAM;YAAU;QACnD;sCAAG;QAAC;KAAU;AAChB;IALa;;QACW;;;AAMjB,MAAM,gBAAgB;;IAC3B,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,SAAiB;YACnC,UAAU;gBAAE,GAAG,OAAO;gBAAE;gBAAS,MAAM;YAAQ;QACjD;oCAAG;QAAC;KAAU;AAChB;IALa;;QACW;;;AAMjB,MAAM,kBAAkB;;IAC7B,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE,CAAC,SAAiB;YACnC,UAAU;gBAAE,GAAG,OAAO;gBAAE;gBAAS,MAAM;YAAU;QACnD;sCAAG;QAAC;KAAU;AAChB;IALa;;QACW;;;AAMjB,MAAM,eAAe;;IAC1B,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oCAAE,CAAC,SAAiB;YACnC,UAAU;gBAAE,GAAG,OAAO;gBAAE;gBAAS,MAAM;YAAO;QAChD;mCAAG;QAAC;KAAU;AAChB;IALa;;QACW", "debugId": null}}]}