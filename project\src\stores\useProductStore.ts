import { create } from 'zustand'
import { Product } from '../domain/entities/Product'
import { ProductFilters } from '../domain/repositories/ProductRepository'
import { GetProductsUseCase, GetProductsResponse } from '../application/use-cases/GetProductsUseCase'
import { MockProductRepository } from '../infrastructure/repositories/MockProductRepository'

interface ProductStore {
  // State
  products: Product[]
  loading: boolean
  error: string | null
  filters: ProductFilters
  sortBy: 'name' | 'price' | 'rating' | 'category' | null
  sortOrder: 'asc' | 'desc'
  searchTerm: string
  categories: string[]
  
  // Pagination
  currentPage: number
  pageSize: number
  totalCount: number
  hasNextPage: boolean
  hasPreviousPage: boolean

  // Actions
  loadProducts: () => Promise<void>
  loadCategories: () => Promise<void>
  setFilters: (filters: Partial<ProductFilters>) => void
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  setSearchTerm: (searchTerm: string) => void
  setPage: (page: number) => void
  clearFilters: () => void
  refreshProducts: () => Promise<void>
}

// Create repository instance
const productRepository = new MockProductRepository()
const getProductsUseCase = new GetProductsUseCase(productRepository)

export const useProductStore = create<ProductStore>((set, get) => ({
  // Initial state
  products: [],
  loading: false,
  error: null,
  filters: {},
  sortBy: null,
  sortOrder: 'asc',
  searchTerm: '',
  categories: [],
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  hasNextPage: false,
  hasPreviousPage: false,

  // Actions
  loadProducts: async () => {
    const state = get()
    set({ loading: true, error: null })

    try {
      const response: GetProductsResponse = await getProductsUseCase.execute({
        filters: {
          ...state.filters,
          searchTerm: state.searchTerm || undefined,
        },
        sortBy: state.sortBy || undefined,
        sortOrder: state.sortOrder,
        page: state.currentPage,
        pageSize: state.pageSize,
      })

      set({
        products: response.products,
        totalCount: response.totalCount,
        hasNextPage: response.hasNextPage,
        hasPreviousPage: response.hasPreviousPage,
        loading: false,
        error: null,
      })
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load products',
        products: [],
      })
    }
  },

  loadCategories: async () => {
    try {
      const categories = await productRepository.getCategories()
      set({ categories })
    } catch (error) {
      console.error('Failed to load categories:', error)
    }
  },

  setFilters: (newFilters: Partial<ProductFilters>) => {
    const state = get()
    set({
      filters: { ...state.filters, ...newFilters },
      currentPage: 1, // Reset to first page when filters change
    })
    // Auto-reload products when filters change
    get().loadProducts()
  },

  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => {
    set({
      sortBy: sortBy as any,
      sortOrder,
      currentPage: 1, // Reset to first page when sorting changes
    })
    // Auto-reload products when sorting changes
    get().loadProducts()
  },

  setSearchTerm: (searchTerm: string) => {
    set({
      searchTerm,
      currentPage: 1, // Reset to first page when search changes
    })
    // Auto-reload products when search changes
    get().loadProducts()
  },

  setPage: (page: number) => {
    set({ currentPage: page })
    // Auto-reload products when page changes
    get().loadProducts()
  },

  clearFilters: () => {
    set({
      filters: {},
      searchTerm: '',
      sortBy: null,
      sortOrder: 'asc',
      currentPage: 1,
    })
    // Auto-reload products when filters are cleared
    get().loadProducts()
  },

  refreshProducts: async () => {
    await get().loadProducts()
  },
}))

// Initialize the store
useProductStore.getState().loadProducts()
useProductStore.getState().loadCategories()
