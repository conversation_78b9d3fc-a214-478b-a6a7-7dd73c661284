{"extends": ["next/core-web-vitals", "prettier", "plugin:jest/recommended"], "plugins": ["jest"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "prefer-const": "error", "no-var": "error", "no-console": "warn", "eqeqeq": "error", "curly": "error", "no-eval": "error", "no-implied-eval": "error", "no-new-func": "error", "no-script-url": "error", "react/jsx-no-target-blank": "error", "react/no-danger": "error", "react/no-danger-with-children": "error"}, "env": {"jest": true}}