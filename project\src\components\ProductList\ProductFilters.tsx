'use client'

import React, { useState, useCallback } from 'react'
import { useProductStore } from '../../stores/useProductStore'
import { Input } from '../UI/Input'
import { Button } from '../UI/Button'

export const ProductFilters = React.memo(() => {
  const {
    filters,
    searchTerm,
    categories,
    setFilters,
    setSearchTerm,
    clearFilters,
  } = useProductStore()

  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  const handleSearchSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    setSearchTerm(localSearchTerm)
  }, [localSearchTerm, setSearchTerm])

  const handleCategoryChange = useCallback((category: string) => {
    setFilters({
      category: filters.category === category ? undefined : category
    })
  }, [filters.category, setFilters])

  const handlePriceRangeChange = useCallback((field: 'minPrice' | 'maxPrice', value: string) => {
    const numValue = value === '' ? undefined : Number(value)
    setFilters({ [field]: numValue })
  }, [setFilters])

  const handleRatingChange = useCallback((rating: number) => {
    setFilters({
      minRating: filters.minRating === rating ? undefined : rating
    })
  }, [filters.minRating, setFilters])

  const handleClearFilters = useCallback(() => {
    setLocalSearchTerm('')
    clearFilters()
  }, [clearFilters])

  return (
    <div className="space-y-4">
      {/* Search */}
      <form onSubmit={handleSearchSubmit} className="flex gap-2">
        <Input
          type="text"
          placeholder="Search services..."
          value={localSearchTerm}
          onChange={(e) => setLocalSearchTerm(e.target.value)}
          leftIcon={
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          }
          className="flex-1"
          aria-label="Search for services"
        />
        <Button type="submit" size="md">
          Search
        </Button>
      </form>

      {/* Advanced Filters Toggle */}
      <div className="flex items-center justify-between">
        <button
          type="button"
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          aria-expanded={showAdvancedFilters}
          aria-controls="advanced-filters"
        >
          {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters
          <svg
            className={`ml-1 h-4 w-4 inline-block transition-transform ${
              showAdvancedFilters ? 'rotate-180' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {(filters.category || filters.minPrice || filters.maxPrice || filters.minRating || searchTerm) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearFilters}
            aria-label="Clear all filters"
          >
            Clear Filters
          </Button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div
          id="advanced-filters"
          className="space-y-4 p-4 bg-gray-50 rounded-lg border"
        >
          {/* Categories */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  type="button"
                  onClick={() => handleCategoryChange(category)}
                  className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                    filters.category === category
                      ? 'bg-blue-100 border-blue-300 text-blue-800'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                  aria-pressed={filters.category === category}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Price Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Price Range (₹)
            </label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="number"
                placeholder="Min price"
                value={filters.minPrice || ''}
                onChange={(e) => handlePriceRangeChange('minPrice', e.target.value)}
                min="0"
                aria-label="Minimum price"
              />
              <Input
                type="number"
                placeholder="Max price"
                value={filters.maxPrice || ''}
                onChange={(e) => handlePriceRangeChange('maxPrice', e.target.value)}
                min="0"
                aria-label="Maximum price"
              />
            </div>
          </div>

          {/* Rating Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Minimum Rating
            </label>
            <div className="flex gap-2">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  type="button"
                  onClick={() => handleRatingChange(rating)}
                  className={`flex items-center px-3 py-1 text-sm rounded border transition-colors ${
                    filters.minRating === rating
                      ? 'bg-yellow-100 border-yellow-300 text-yellow-800'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                  aria-pressed={filters.minRating === rating}
                  aria-label={`Filter by minimum ${rating} star rating`}
                >
                  <span className="mr-1">{rating}</span>
                  <svg className="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
})

ProductFilters.displayName = 'ProductFilters'
