import { Product } from '../entities/Product'

export interface ProductFilters {
  category?: string
  minPrice?: number
  maxPrice?: number
  minRating?: number
  searchTerm?: string
  tags?: string[]
}

export interface ProductRepository {
  findAll(filters?: ProductFilters): Promise<Product[]>
  findById(id: string): Promise<Product | null>
  findByCategory(category: string): Promise<Product[]>
  search(searchTerm: string): Promise<Product[]>
  getCategories(): Promise<string[]>
}

export interface ProductRepositoryError extends Error {
  code: string
  retryable: boolean
}

export class ProductNotFoundError extends Error implements ProductRepositoryError {
  code = 'PRODUCT_NOT_FOUND'
  retryable = false

  constructor(productId: string) {
    super(`Product with ID ${productId} not found`)
    this.name = 'ProductNotFoundError'
  }
}

export class ProductRepositoryConnectionError extends Error implements ProductRepositoryError {
  code = 'CONNECTION_ERROR'
  retryable = true

  constructor(message: string) {
    super(message)
    this.name = 'ProductRepositoryConnectionError'
  }
}

export class ProductRepositoryTimeoutError extends Error implements ProductRepositoryError {
  code = 'TIMEOUT_ERROR'
  retryable = true

  constructor(message: string) {
    super(message)
    this.name = 'ProductRepositoryTimeoutError'
  }
}
