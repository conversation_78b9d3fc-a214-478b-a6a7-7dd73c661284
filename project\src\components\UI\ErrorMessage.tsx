'use client'

import React from 'react'
import { clsx } from 'clsx'
import { But<PERSON> } from './Button'

interface ErrorMessageProps {
  message: string
  onRetry?: () => void
  className?: string
  title?: string
}

export const ErrorMessage = React.memo<ErrorMessageProps>(({
  message,
  onRetry,
  className,
  title = 'Something went wrong',
}) => {
  return (
    <div
      className={clsx(
        'bg-red-50 border border-red-200 rounded-lg p-6 text-center',
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <div className="flex justify-center mb-4">
        <svg
          className="h-12 w-12 text-red-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      </div>
      
      <h3 className="text-lg font-medium text-red-800 mb-2">
        {title}
      </h3>
      
      <p className="text-red-700 mb-4">
        {message}
      </p>
      
      {onRetry && (
        <Button
          onClick={onRetry}
          variant="outline"
          size="sm"
        >
          Try Again
        </Button>
      )}
    </div>
  )
})

ErrorMessage.displayName = 'ErrorMessage'
