import { Product } from '../../domain/entities/Product'
import { CartService, CartState } from '../../domain/services/CartService'
import { ProductRepository } from '../../domain/repositories/ProductRepository'

export interface AddToCartRequest {
  productId: string
  quantity?: number
}

export interface AddToCartResponse {
  success: boolean
  cart: CartState
  addedProduct: Product
  message: string
}

export class AddToCartUseCase {
  constructor(private productRepository: ProductRepository) {}

  async execute(
    request: AddToCartRequest, 
    currentCart: CartState
  ): Promise<AddToCartResponse> {
    try {
      // Validate input
      if (!request.productId || request.productId.trim().length === 0) {
        throw new Error('Product ID is required')
      }

      const quantity = request.quantity || 1
      if (quantity <= 0) {
        throw new Error('Quantity must be greater than 0')
      }

      if (quantity > 10) {
        throw new Error('Cannot add more than 10 items at once')
      }

      // Find the product
      const product = await this.productRepository.findById(request.productId)
      if (!product) {
        throw new Error(`Product with ID ${request.productId} not found`)
      }

      // Check if product is available
      if (!product.isAvailable) {
        throw new Error(`Product ${product.name} is currently unavailable`)
      }

      // Check if adding this quantity would exceed reasonable limits
      const existingItem = CartService.getItem(currentCart, request.productId)
      const newTotalQuantity = (existingItem?.quantity || 0) + quantity
      
      if (newTotalQuantity > 50) {
        throw new Error('Cannot have more than 50 of the same item in cart')
      }

      // Add to cart
      const updatedCart = CartService.addItem(currentCart, product, quantity)

      // Validate the updated cart
      if (!CartService.validateCart(updatedCart)) {
        throw new Error('Cart validation failed after adding item')
      }

      const message = existingItem 
        ? `Updated ${product.name} quantity to ${newTotalQuantity}`
        : `Added ${product.name} to cart`

      return {
        success: true,
        cart: updatedCart,
        addedProduct: product,
        message,
      }
    } catch (error) {
      throw new Error(`Failed to add item to cart: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}
