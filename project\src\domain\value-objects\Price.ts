export class Price {
  private constructor(
    private readonly _amount: number,
    private readonly _currency: string
  ) {
    this.validate()
  }

  static create(amount: number, currency: string = 'INR'): Price {
    return new Price(amount, currency)
  }

  private validate(): void {
    if (this._amount < 0) {
      throw new Error('Price amount cannot be negative')
    }
    if (!this._currency || this._currency.trim().length === 0) {
      throw new Error('Currency is required')
    }
    if (this._currency.length !== 3) {
      throw new Error('Currency must be a 3-letter code')
    }
  }

  get amount(): number {
    return this._amount
  }

  get currency(): string {
    return this._currency
  }

  format(): string {
    const currencySymbols: Record<string, string> = {
      INR: '₹',
      USD: '$',
      EUR: '€',
      GBP: '£',
    }

    const symbol = currencySymbols[this._currency] || this._currency
    return `${symbol}${this._amount.toLocaleString()}`
  }

  equals(other: Price): boolean {
    return this._amount === other._amount && this._currency === other._currency
  }

  add(other: Price): Price {
    if (this._currency !== other._currency) {
      throw new Error('Cannot add prices with different currencies')
    }
    return new Price(this._amount + other._amount, this._currency)
  }

  multiply(factor: number): Price {
    if (factor < 0) {
      throw new Error('Factor cannot be negative')
    }
    return new Price(this._amount * factor, this._currency)
  }

  isGreaterThan(other: Price): boolean {
    if (this._currency !== other._currency) {
      throw new Error('Cannot compare prices with different currencies')
    }
    return this._amount > other._amount
  }

  isLessThan(other: Price): boolean {
    if (this._currency !== other._currency) {
      throw new Error('Cannot compare prices with different currencies')
    }
    return this._amount < other._amount
  }
}
