'use client'

import React, { useState, useCallback } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { clsx } from 'clsx'
import { Product } from '../../domain/entities/Product'
import { useCartStore } from '../../stores/useCartStore'
import { useSuccessToast, useErrorToast } from '../Toast/ToastContainer'
import { Button } from '../UI/Button'

interface ProductCardProps {
  product: Product
  className?: string
}

export const ProductCard = React.memo<ProductCardProps>(({ product, className }) => {
  const [isLoading, setIsLoading] = useState(false)
  const [imageError, setImageError] = useState(false)
  
  const { addItem, hasItem, getItemQuantity, updateItemQuantity } = useCartStore()
  const showSuccessToast = useSuccessToast()
  const showErrorToast = useErrorToast()

  const isInCart = hasItem(product.id)
  const currentQuantity = getItemQuantity(product.id)

  const handleAddToCart = useCallback(async () => {
    if (!product.isAvailable) {
      showErrorToast('This product is currently unavailable')
      return
    }

    setIsLoading(true)
    try {
      addItem(product, 1)
      showSuccessToast(`Added ${product.name} to cart`, {
        duration: 3000,
        action: {
          label: 'View Cart',
          onClick: () => {
            // Navigate to cart - would implement with router
            console.log('Navigate to cart')
          }
        }
      })
    } catch (error) {
      showErrorToast(error instanceof Error ? error.message : 'Failed to add item to cart')
    } finally {
      setIsLoading(false)
    }
  }, [product, addItem, showSuccessToast, showErrorToast])

  const handleQuantityChange = useCallback((newQuantity: number) => {
    try {
      updateItemQuantity(product.id, newQuantity)
      showSuccessToast(`Updated ${product.name} quantity to ${newQuantity}`)
    } catch (error) {
      showErrorToast(error instanceof Error ? error.message : 'Failed to update quantity')
    }
  }, [product.id, product.name, updateItemQuantity, showSuccessToast, showErrorToast])

  const handleImageError = useCallback(() => {
    setImageError(true)
  }, [])

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      handleAddToCart()
    }
  }, [handleAddToCart])

  return (
    <motion.article
      className={clsx(
        'group relative bg-white rounded-lg shadow-sm border border-gray-200',
        'hover:shadow-md transition-shadow duration-200',
        'focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      role="article"
      aria-labelledby={`product-${product.id}-name`}
    >
      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden rounded-t-lg bg-gray-100">
        {!imageError ? (
          <Image
            src={product.imageUrl}
            alt={`${product.name} - ${product.description}`}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-200"
            onError={handleImageError}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gray-200">
            <svg
              className="h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        )}
        
        {/* Availability Badge */}
        {!product.isAvailable && (
          <div className="absolute top-2 left-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded">
            Unavailable
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3
            id={`product-${product.id}-name`}
            className="text-lg font-semibold text-gray-900 line-clamp-2"
          >
            {product.name}
          </h3>
          <button
            type="button"
            className="ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
            aria-label={`Add ${product.name} to favorites`}
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
          </button>
        </div>

        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {product.description}
        </p>

        {/* Rating and Reviews */}
        <div className="flex items-center mb-3">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <svg
                key={i}
                className={clsx(
                  'h-4 w-4',
                  i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'
                )}
                fill="currentColor"
                viewBox="0 0 20 20"
                aria-hidden="true"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
          <span className="ml-2 text-sm text-gray-500">
            {product.rating} ({product.reviewCount} reviews)
          </span>
        </div>

        {/* Duration and Category */}
        <div className="flex items-center justify-between mb-4">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {product.category}
          </span>
          <span className="text-sm text-gray-500">
            ⏱️ {product.duration}
          </span>
        </div>

        {/* Price and Actions */}
        <div className="flex items-center justify-between">
          <div>
            <span className="text-2xl font-bold text-gray-900">
              {product.formattedPrice}
            </span>
          </div>

          {!isInCart ? (
            <Button
              onClick={handleAddToCart}
              onKeyDown={handleKeyDown}
              loading={isLoading}
              disabled={!product.isAvailable}
              size="sm"
              aria-label={`Add ${product.name} to cart for ${product.formattedPrice}`}
            >
              Add to Cart
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => handleQuantityChange(currentQuantity - 1)}
                className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center"
                aria-label="Decrease quantity"
                disabled={currentQuantity <= 1}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              
              <span
                className="w-8 text-center font-medium"
                aria-label={`Current quantity: ${currentQuantity}`}
              >
                {currentQuantity}
              </span>
              
              <button
                type="button"
                onClick={() => handleQuantityChange(currentQuantity + 1)}
                className="w-8 h-8 rounded-full bg-blue-100 hover:bg-blue-200 flex items-center justify-center"
                aria-label="Increase quantity"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>
    </motion.article>
  )
})

ProductCard.displayName = 'ProductCard'
