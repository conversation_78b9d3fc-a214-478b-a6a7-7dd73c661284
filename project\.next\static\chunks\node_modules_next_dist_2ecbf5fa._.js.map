{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/build/polyfills/process.ts"], "sourcesContent": ["module.exports =\n  global.process?.env && typeof global.process?.env === 'object'\n    ? global.process\n    : require('next/dist/compiled/process')\n"], "names": ["global", "module", "exports", "process", "env", "require"], "mappings": ";IACEA,iBAA8BA;AADhCC,OAAOC,OAAO,GACZF,CAAAA,CAAAA,kBAAAA,OAAOG,OAAO,KAAA,OAAA,KAAA,IAAdH,gBAAgBI,GAAG,KAAI,OAAA,CAAA,CAAOJ,mBAAAA,OAAOG,OAAO,KAAA,OAAA,KAAA,IAAdH,iBAAgBI,GAAG,MAAK,WAClDJ,OAAOG,OAAO,GACdE,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/dist/build/polyfills/polyfill-module.js"], "sourcesContent": ["\"trimStart\"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),\"trimEnd\"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),\"description\"in Symbol.prototype||Object.defineProperty(Symbol.prototype,\"description\",{configurable:!0,get:function(){var t=/\\((.*)\\)/.exec(this.toString());return t?t[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(t,r){return r=this.concat.apply([],this),t>1&&r.some(Array.isArray)?r.flat(t-1):r},Array.prototype.flatMap=function(t,r){return this.map(t,r).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(t){if(\"function\"!=typeof t)return this.then(t,t);var r=this.constructor||Promise;return this.then(function(n){return r.resolve(t()).then(function(){return n})},function(n){return r.resolve(t()).then(function(){throw n})})}),Object.fromEntries||(Object.fromEntries=function(t){return Array.from(t).reduce(function(t,r){return t[r[0]]=r[1],t},{})}),Array.prototype.at||(Array.prototype.at=function(t){var r=Math.trunc(t)||0;if(r<0&&(r+=this.length),!(r<0||r>=this.length))return this[r]}),Object.hasOwn||(Object.hasOwn=function(t,r){if(null==t)throw new TypeError(\"Cannot convert undefined or null to object\");return Object.prototype.hasOwnProperty.call(Object(t),r)}),\"canParse\"in URL||(URL.canParse=function(t,r){try{return!!new URL(t,r)}catch(t){return!1}});\n"], "names": [], "mappings": "AAAA,eAAc,OAAO,SAAS,IAAE,CAAC,OAAO,SAAS,CAAC,SAAS,GAAC,OAAO,SAAS,CAAC,QAAQ,GAAE,aAAY,OAAO,SAAS,IAAE,CAAC,OAAO,SAAS,CAAC,OAAO,GAAC,OAAO,SAAS,CAAC,SAAS,GAAE,iBAAgB,OAAO,SAAS,IAAE,OAAO,cAAc,CAAC,OAAO,SAAS,EAAC,eAAc;IAAC,cAAa,CAAC;IAAE,KAAI;QAAW,IAAI,IAAE,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ;QAAI,OAAO,IAAE,CAAC,CAAC,EAAE,GAAC,KAAK;IAAC;AAAC,IAAG,MAAM,SAAS,CAAC,IAAI,IAAE,CAAC,MAAM,SAAS,CAAC,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,IAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAC,IAAI,GAAE,IAAE,KAAG,EAAE,IAAI,CAAC,MAAM,OAAO,IAAE,EAAE,IAAI,CAAC,IAAE,KAAG;AAAC,GAAE,MAAM,SAAS,CAAC,OAAO,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAE,GAAG,IAAI;AAAE,CAAC,GAAE,QAAQ,SAAS,CAAC,OAAO,IAAE,CAAC,QAAQ,SAAS,CAAC,OAAO,GAAC,SAAS,CAAC;IAAE,IAAG,cAAY,OAAO,GAAE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAE;IAAG,IAAI,IAAE,IAAI,CAAC,WAAW,IAAE;IAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YAAW,OAAO;QAAC;IAAE,GAAE,SAAS,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YAAW,MAAM;QAAC;IAAE;AAAE,CAAC,GAAE,OAAO,WAAW,IAAE,CAAC,OAAO,WAAW,GAAC,SAAS,CAAC;IAAE,OAAO,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,EAAC,CAAC;QAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,EAAE,EAAC;IAAC,GAAE,CAAC;AAAE,CAAC,GAAE,MAAM,SAAS,CAAC,EAAE,IAAE,CAAC,MAAM,SAAS,CAAC,EAAE,GAAC,SAAS,CAAC;IAAE,IAAI,IAAE,KAAK,KAAK,CAAC,MAAI;IAAE,IAAG,IAAE,KAAG,CAAC,KAAG,IAAI,CAAC,MAAM,GAAE,CAAC,CAAC,IAAE,KAAG,KAAG,IAAI,CAAC,MAAM,GAAE,OAAO,IAAI,CAAC,EAAE;AAAA,CAAC,GAAE,OAAO,MAAM,IAAE,CAAC,OAAO,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAG,QAAM,GAAE,MAAM,IAAI,UAAU;IAA8C,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,IAAG;AAAE,CAAC,GAAE,cAAa,OAAK,CAAC,IAAI,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAG;QAAC,OAAM,CAAC,CAAC,IAAI,IAAI,GAAE;IAAE,EAAC,OAAM,GAAE;QAAC,OAAM,CAAC;IAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/lib/require-instrumentation-client.ts"], "sourcesContent": ["/**\n * This module imports the client instrumentation hook from the project root.\n *\n * The `private-next-instrumentation-client` module is automatically aliased to\n * the `instrumentation-client.ts` file in the project root by webpack or turbopack.\n */\nif (process.env.NODE_ENV === 'development') {\n  const measureName = 'Client Instrumentation Hook'\n  const startTime = performance.now()\n  module.exports = require('private-next-instrumentation-client')\n  const endTime = performance.now()\n\n  const duration = endTime - startTime\n  performance.measure(measureName, {\n    start: startTime,\n    end: endTime,\n    detail: 'Client instrumentation initialization',\n  })\n\n  // Using 16ms threshold as it represents one frame (1000ms/60fps)\n  // This helps identify if the instrumentation hook initialization\n  // could potentially cause frame drops during development.\n  const THRESHOLD = 16\n  if (duration > THRESHOLD) {\n    console.log(\n      `[${measureName}] Slow execution detected: ${duration.toFixed(0)}ms (Note: Code download overhead is not included in this measurement)`\n    )\n  }\n} else {\n  module.exports = require('private-next-instrumentation-client')\n}\n"], "names": ["process", "env", "NODE_ENV", "measureName", "startTime", "performance", "now", "module", "exports", "require", "endTime", "duration", "measure", "start", "end", "detail", "THRESHOLD", "console", "log", "toFixed"], "mappings": "AAAA;;;;;CAKC,GACGA,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAD5B;AACD,wCAA4C;IAC1C,MAAMC,cAAc;IACpB,MAAMC,YAAYC,YAAYC,GAAG;IACjCC,OAAOC,OAAO,GAAGC,QAAQ;IACzB,MAAMC,UAAUL,YAAYC,GAAG;IAE/B,MAAMK,WAAWD,UAAUN;IAC3BC,YAAYO,OAAO,CAACT,aAAa;QAC/BU,OAAOT;QACPU,KAAKJ;QACLK,QAAQ;IACV;IAEA,iEAAiE;IACjE,iEAAiE;IACjE,0DAA0D;IAC1D,MAAMC,YAAY;IAClB,IAAIL,WAAWK,WAAW;QACxBC,QAAQC,GAAG,CACT,CAAC,CAAC,EAAEf,YAAY,2BAA2B,EAAEQ,SAASQ,OAAO,CAAC,GAAG,qEAAqE,CAAC;IAE3I;AACF,OAAO;;AAEP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/lib/is-error.ts"], "sourcesContent": ["import { isPlainObject } from '../shared/lib/is-plain-object'\n\n// We allow some additional attached properties for Next.js errors\nexport interface NextError extends Error {\n  type?: string\n  page?: string\n  code?: string | number\n  cancelled?: boolean\n  digest?: number\n}\n\n/**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */\nexport default function isError(err: unknown): err is NextError {\n  return (\n    typeof err === 'object' && err !== null && 'name' in err && 'message' in err\n  )\n}\n\nfunction safeStringify(obj: any) {\n  const seen = new WeakSet()\n\n  return JSON.stringify(obj, (_key, value) => {\n    // If value is an object and already seen, replace with \"[Circular]\"\n    if (typeof value === 'object' && value !== null) {\n      if (seen.has(value)) {\n        return '[Circular]'\n      }\n      seen.add(value)\n    }\n    return value\n  })\n}\n\nexport function getProperError(err: unknown): Error {\n  if (isError(err)) {\n    return err\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // provide better error for case where `throw undefined`\n    // is called in development\n    if (typeof err === 'undefined') {\n      return new Error(\n        'An undefined error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n\n    if (err === null) {\n      return new Error(\n        'A null error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n  }\n\n  return new Error(isPlainObject(err) ? safeStringify(err) : err + '')\n}\n"], "names": ["isError", "getProperError", "err", "safeStringify", "obj", "seen", "WeakSet", "JSON", "stringify", "_key", "value", "has", "add", "process", "env", "NODE_ENV", "Error", "isPlainObject"], "mappings": "AAyCMa,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;IA9B/B;;;CAGC,GACD,OAIC,EAAA;eAJuBf;;IAqBRC,cAAc,EAAA;eAAdA;;;+BApCc;AAef,SAASD,QAAQE,GAAY;IAC1C,OACE,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,UAAUA,OAAO,aAAaA;AAE7E;AAEA,SAASC,cAAcC,GAAQ;IAC7B,MAAMC,OAAO,IAAIC;IAEjB,OAAOC,KAAKC,SAAS,CAACJ,KAAK,CAACK,MAAMC;QAChC,oEAAoE;QACpE,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM;YAC/C,IAAIL,KAAKM,GAAG,CAACD,QAAQ;gBACnB,OAAO;YACT;YACAL,KAAKO,GAAG,CAACF;QACX;QACA,OAAOA;IACT;AACF;AAEO,SAAST,eAAeC,GAAY;IACzC,IAAIF,QAAQE,MAAM;QAChB,OAAOA;IACT;IAEA,wCAA4C;QAC1C,wDAAwD;QACxD,2BAA2B;QAC3B,IAAI,OAAOA,QAAQ,aAAa;YAC9B,OAAO,OAAA,cAGN,CAHM,IAAIc,MACT,oCACE,6EAFG,qBAAA;uBAAA;4BAAA;8BAAA;YAGP;QACF;QAEA,IAAId,QAAQ,MAAM;YAChB,OAAO,OAAA,cAGN,CAHM,IAAIc,MACT,8BACE,6EAFG,qBAAA;uBAAA;4BAAA;8BAAA;YAGP;QACF;IACF;IAEA,OAAO,OAAA,cAA6D,CAA7D,IAAIA,MAAMC,CAAAA,GAAAA,eAAAA,aAAa,EAACf,OAAOC,cAAcD,OAAOA,MAAM,KAA1D,qBAAA;eAAA;oBAAA;sBAAA;IAA4D;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/lib/error-telemetry-utils.ts"], "sourcesContent": ["const ERROR_CODE_DELIMITER = '@'\n\n/**\n * Augments the digest field of errors thrown in React Server Components (RSC) with an error code.\n * Since RSC errors can only be serialized through the digest field, this provides a way to include\n * an additional error code that can be extracted client-side via `extractNextErrorCode`.\n *\n * The error code is appended to the digest string with a semicolon separator, allowing it to be\n * parsed out later while preserving the original digest value.\n */\nexport const createDigestWithErrorCode = (\n  thrownValue: unknown,\n  originalDigest: string\n): string => {\n  if (\n    typeof thrownValue === 'object' &&\n    thrownValue !== null &&\n    '__NEXT_ERROR_CODE' in thrownValue\n  ) {\n    return `${originalDigest}${ERROR_CODE_DELIMITER}${thrownValue.__NEXT_ERROR_CODE}`\n  }\n  return originalDigest\n}\n\n/**\n * Copies the error code from one error to another by setting the __NEXT_ERROR_CODE property.\n * This allows error codes to be preserved when wrapping or transforming errors.\n */\nexport const copyNextErrorCode = (source: unknown, target: unknown): void => {\n  const errorCode = extractNextErrorCode(source)\n  if (errorCode && typeof target === 'object' && target !== null) {\n    Object.defineProperty(target, '__NEXT_ERROR_CODE', {\n      value: errorCode,\n      enumerable: false,\n      configurable: true,\n    })\n  }\n}\n\nexport const extractNextErrorCode = (error: unknown): string | undefined => {\n  if (\n    typeof error === 'object' &&\n    error !== null &&\n    '__NEXT_ERROR_CODE' in error &&\n    typeof error.__NEXT_ERROR_CODE === 'string'\n  ) {\n    return error.__NEXT_ERROR_CODE\n  }\n\n  if (\n    typeof error === 'object' &&\n    error !== null &&\n    'digest' in error &&\n    typeof error.digest === 'string'\n  ) {\n    const segments = error.digest.split(ERROR_CODE_DELIMITER)\n    const errorCode = segments.find((segment) => segment.startsWith('E'))\n    return errorCode\n  }\n\n  return undefined\n}\n"], "names": ["copyNextErrorCode", "createDigestWithErrorCode", "extractNextErrorCode", "ERROR_CODE_DELIMITER", "thrownValue", "originalDigest", "__NEXT_ERROR_CODE", "source", "target", "errorCode", "Object", "defineProperty", "value", "enumerable", "configurable", "error", "digest", "segments", "split", "find", "segment", "startsWith", "undefined"], "mappings": ";;;;;;;;;;;;;;;;IA4BaA,iBAAiB,EAAA;eAAjBA;;IAlBAC,yBAAyB,EAAA;eAAzBA;;IA6BAC,oBAAoB,EAAA;eAApBA;;;AAvCb,MAAMC,uBAAuB;AAUtB,MAAMF,4BAA4B,CACvCG,aACAC;IAEA,IACE,OAAOD,gBAAgB,YACvBA,gBAAgB,QAChB,uBAAuBA,aACvB;QACA,OAAO,GAAGC,iBAAiBF,uBAAuBC,YAAYE,iBAAiB,EAAE;IACnF;IACA,OAAOD;AACT;AAMO,MAAML,oBAAoB,CAACO,QAAiBC;IACjD,MAAMC,YAAYP,qBAAqBK;IACvC,IAAIE,aAAa,OAAOD,WAAW,YAAYA,WAAW,MAAM;QAC9DE,OAAOC,cAAc,CAACH,QAAQ,qBAAqB;YACjDI,OAAOH;YACPI,YAAY;YACZC,cAAc;QAChB;IACF;AACF;AAEO,MAAMZ,uBAAuB,CAACa;IACnC,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,uBAAuBA,SACvB,OAAOA,MAAMT,iBAAiB,KAAK,UACnC;QACA,OAAOS,MAAMT,iBAAiB;IAChC;IAEA,IACE,OAAOS,UAAU,YACjBA,UAAU,QACV,YAAYA,SACZ,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,MAAMC,WAAWF,MAAMC,MAAM,CAACE,KAAK,CAACf;QACpC,MAAMM,YAAYQ,SAASE,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC;QAChE,OAAOZ;IACT;IAEA,OAAOa;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": "AAiBQQ,QAAQC,GAAG,CAACC,YAAY,KAAK;;;;;;;;;;;;;;;;;;IA2BrBV,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAAWE;IAC3C,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,uCAAyC;;QAEzC,OAAO;6KACLC,UAAAA,CAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAAWG;IAC1C,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/is-plain-object.ts"], "sourcesContent": ["export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n"], "names": ["getObjectClassLabel", "isPlainObject", "value", "Object", "prototype", "toString", "call", "getPrototypeOf", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,mBAAmB,EAAA;eAAnBA;;IAIAC,aAAa,EAAA;eAAbA;;;AAJT,SAASD,oBAAoBE,KAAU;IAC5C,OAAOC,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ;AACxC;AAEO,SAASD,cAAcC,KAAU;IACtC,IAAIF,oBAAoBE,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAME,YAAYD,OAAOI,cAAc,CAACL;IAExC;;;;;;;;GAQC,GACD,OAAOE,cAAc,QAAQA,UAAUI,cAAc,CAAC;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/head-manager-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\n\nexport const HeadManagerContext: React.Context<{\n  updateHead?: (state: any) => void\n  mountedInstances?: any\n  updateScripts?: (state: any) => void\n  scripts?: any\n  getIsSsr?: () => boolean\n\n  // Used in app directory, to render script tags as server components.\n  appDir?: boolean\n  nonce?: string\n}> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  HeadManagerContext.displayName = 'HeadManagerContext'\n}\n"], "names": ["HeadManagerContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAcIG,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAZhBL,sBAAAA;;;eAAAA;;;;gEAFK;AAEX,MAAMA,qBAURC,OAAAA,OAAK,CAACC,aAAa,CAAC,CAAC;AAE1B,wCAA2C;IACzCF,mBAAmBM,WAAW,GAAG;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/hooks-client-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport { createContext } from 'react'\nimport type { Params } from '../../server/request/params'\n\nexport const SearchParamsContext = createContext<URLSearchParams | null>(null)\nexport const PathnameContext = createContext<string | null>(null)\nexport const PathParamsContext = createContext<Params | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  SearchParamsContext.displayName = 'SearchParamsContext'\n  PathnameContext.displayName = 'PathnameContext'\n  PathParamsContext.displayName = 'PathParamsContext'\n}\n"], "names": ["PathParamsContext", "PathnameContext", "SearchParamsContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AASII,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAT7B;;;;;;;;;;;;;;;;;IAOaN,iBAAiB,EAAA;eAAjBA;;IADAC,eAAe,EAAA;eAAfA;;IADAC,mBAAmB,EAAA;eAAnBA;;;uBAHiB;AAGvB,MAAMA,sBAAsBC,CAAAA,GAAAA,OAAAA,aAAa,EAAyB;AAClE,MAAMF,kBAAkBE,CAAAA,GAAAA,OAAAA,aAAa,EAAgB;AACrD,MAAMH,oBAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAAgB;AAE9D,wCAA2C;IACzCD,oBAAoBK,WAAW,GAAG;IAClCN,gBAAgBM,WAAW,GAAG;IAC9BP,kBAAkBO,WAAW,GAAG;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GAAA;;;;+BACeA,cAAAA;;;eAAAA;;;AAAT,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/hash.ts"], "sourcesContent": ["// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n"], "names": ["djb2Hash", "hexHash", "str", "hash", "i", "length", "char", "charCodeAt", "toString", "slice"], "mappings": "AAAA,wCAAwC;AACxC,4CAA4C;AAC5C,iHAAiH;AACjH,wFAAwF;AACxF,gGAAgG;AAChG,wHAAwH;AACxH,wDAAwD;;;;;;;;;;;;;;;;IACxCA,QAAQ,EAAA;eAARA;;IASAC,OAAO,EAAA;eAAPA;;;AATT,SAASD,SAASE,GAAW;IAClC,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,OAAOJ,IAAIK,UAAU,CAACH;QAC5BD,OAASA,CAAAA,QAAQ,CAAA,IAAKA,OAAOG,OAAQ;IACvC;IACA,OAAOH,SAAS;AAClB;AAEO,SAASF,QAAQC,GAAW;IACjC,OAAOF,SAASE,KAAKM,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;;IA4BaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IATAC,cAAc,EAAA;eAAdA;;IAKAC,sBAAsB,EAAA;eAAtBA;;;AALT,SAASD,eAAeE,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASF,uBAAuBC,OAAe;IACpD,OAAOA,QAAQE,UAAU,CAAC,QAAQF,YAAY;AAChD;AAEO,SAASH,6BACdG,OAAgB,EAChBG,YAA2D;IAE3D,MAAMC,gBAAgBJ,QAAQK,QAAQ,CAACT;IAEvC,IAAIQ,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBV,mBAAmB,MAAMU,mBACzBV;IACN;IAEA,OAAOI;AACT;AAEO,MAAMJ,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GAAA;;;;+BACeA,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["normalizeAppPath", "normalizeRscURL", "route", "ensureLeadingSlash", "split", "reduce", "pathname", "segment", "index", "segments", "isGroupSegment", "length", "url", "replace"], "mappings": ";;;;;;;;;;;;;;;IAsBgBA,gBAAgB,EAAA;eAAhBA;;IAmCAC,eAAe,EAAA;eAAfA;;;oCAzDmB;yBACJ;AAqBxB,SAASD,iBAAiBE,KAAa;IAC5C,OAAOC,CAAAA,GAAAA,oBAAAA,kBAAkB,EACvBD,MAAME,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,IAAII,CAAAA,GAAAA,SAAAA,cAAc,EAACH,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASE,MAAM,GAAG,GAC5B;YACA,OAAOL;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASN,gBAAgBW,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "interceptingRoute", "marker", "interceptedRoute", "Error", "normalizeAppPath", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;;;;;;;;;IAGaA,0BAA0B,EAAA;eAA1BA;;IAkBGC,mCAAmC,EAAA;eAAnCA;;IAXAC,0BAA0B,EAAA;eAA1BA;;;0BAViB;AAG1B,MAAMF,6BAA6B;IACxC;IACA;IACA;IACA;CACD;AAEM,SAASE,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLN,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASR,oCAAoCE,IAAY;IAC9D,IAAIO,mBACFC,QACAC;IAEF,KAAK,MAAMN,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCO,SAASX,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAII,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGT,KAAKC,KAAK,CAACO,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BV,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAO,oBAAoBI,CAAAA,GAAAA,UAAAA,gBAAgB,EAACJ,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BV,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAS,mBAAmBF,kBAChBN,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIL,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMM,yBAAyBR,kBAAkBN,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIN,MACP,iCAA8BV,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAS,mBAAmBM,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIJ,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/app-router-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport type { FetchServerResponseResult } from '../../client/components/router-reducer/fetch-server-response'\nimport type {\n  FocusAndScrollRef,\n  PrefetchKind,\n} from '../../client/components/router-reducer/router-reducer-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport React from 'react'\n\nexport type ChildSegmentMap = Map<string, CacheNode>\n\n/**\n * Cache node used in app-router / layout-router.\n */\nexport type CacheNode = ReadyCacheNode | LazyCacheNode\n\nexport type LoadingModuleData =\n  | [React.JSX.Element, React.ReactNode, React.ReactNode]\n  | null\n\n/** viewport metadata node */\nexport type HeadData = React.ReactNode\n\nexport type LazyCacheNode = {\n  /**\n   * When rsc is null, this is a lazily-initialized cache node.\n   *\n   * If the app attempts to render it, it triggers a lazy data fetch,\n   * postpones the render, and schedules an update to a new tree.\n   *\n   * TODO: This mechanism should not be used when PPR is enabled, though it\n   * currently is in some cases until we've implemented partial\n   * segment fetching.\n   */\n  rsc: null\n\n  /**\n   * A prefetched version of the segment data. See explanation in corresponding\n   * field of ReadyCacheNode (below).\n   *\n   * Since LazyCacheNode mostly only exists in the non-PPR implementation, this\n   * will usually be null, but it could have been cloned from a previous\n   * CacheNode that was created by the PPR implementation. Eventually we want\n   * to migrate everything away from LazyCacheNode entirely.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * A pending response for the lazy data fetch. If this is not present\n   * during render, it is lazily created.\n   */\n  lazyData: Promise<FetchServerResponseResult> | null\n\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  /**\n   * Child parallel routes.\n   */\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  /**\n   * The timestamp of the navigation that last updated the CacheNode's data. If\n   * a CacheNode is reused from a previous navigation, this value is not\n   * updated. Used to track the staleness of the data.\n   */\n  navigatedAt: number\n}\n\nexport type ReadyCacheNode = {\n  /**\n   * When rsc is not null, it represents the RSC data for the\n   * corresponding segment.\n   *\n   * `null` is a valid React Node but because segment data is always a\n   * <LayoutRouter> component, we can use `null` to represent empty.\n   *\n   * TODO: For additional type safety, update this type to\n   * Exclude<React.ReactNode, null>. Need to update createEmptyCacheNode to\n   * accept rsc as an argument, or just inline the callers.\n   */\n  rsc: React.ReactNode\n\n  /**\n   * Represents a static version of the segment that can be shown immediately,\n   * and may or may not contain dynamic holes. It's prefetched before a\n   * navigation occurs.\n   *\n   * During rendering, we will choose whether to render `rsc` or `prefetchRsc`\n   * with `useDeferredValue`. As with the `rsc` field, a value of `null` means\n   * no value was provided. In this case, the LayoutRouter will go straight to\n   * rendering the `rsc` value; if that one is also missing, it will suspend and\n   * trigger a lazy fetch.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * There should never be a lazy data request in this case.\n   */\n  lazyData: null\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  navigatedAt: number\n}\n\nexport interface NavigateOptions {\n  scroll?: boolean\n}\n\nexport interface PrefetchOptions {\n  kind: PrefetchKind\n}\n\nexport interface AppRouterInstance {\n  /**\n   * Navigate to the previous history entry.\n   */\n  back(): void\n  /**\n   * Navigate to the next history entry.\n   */\n  forward(): void\n  /**\n   * Refresh the current page.\n   */\n  refresh(): void\n  /**\n   * Refresh the current page. Use in development only.\n   * @internal\n   */\n  hmrRefresh(): void\n  /**\n   * Navigate to the provided href.\n   * Pushes a new history entry.\n   */\n  push(href: string, options?: NavigateOptions): void\n  /**\n   * Navigate to the provided href.\n   * Replaces the current history entry.\n   */\n  replace(href: string, options?: NavigateOptions): void\n  /**\n   * Prefetch the provided href.\n   */\n  prefetch(href: string, options?: PrefetchOptions): void\n}\n\nexport const AppRouterContext = React.createContext<AppRouterInstance | null>(\n  null\n)\nexport const LayoutRouterContext = React.createContext<{\n  parentTree: FlightRouterState\n  parentCacheNode: CacheNode\n  parentSegmentPath: FlightSegmentPath | null\n  url: string\n} | null>(null)\n\nexport const GlobalLayoutRouterContext = React.createContext<{\n  tree: FlightRouterState\n  focusAndScrollRef: FocusAndScrollRef\n  nextUrl: string | null\n}>(null as any)\n\nexport const TemplateContext = React.createContext<React.ReactNode>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  AppRouterContext.displayName = 'AppRouterContext'\n  LayoutRouterContext.displayName = 'LayoutRouterContext'\n  GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext'\n  TemplateContext.displayName = 'TemplateContext'\n}\n\nexport const MissingSlotContext = React.createContext<Set<string>>(new Set())\n"], "names": ["AppRouterContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "MissingSlotContext", "TemplateContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "Set"], "mappings": "AAkLIO,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlL7B;;;;;;;;;;;;;;;;;;;IAgKaT,gBAAgB,EAAA;eAAhBA;;IAUAC,yBAAyB,EAAA;eAAzBA;;IAPAC,mBAAmB,EAAA;eAAnBA;;IAsBAC,kBAAkB,EAAA;eAAlBA;;IATAC,eAAe,EAAA;eAAfA;;;;gEArKK;AAqJX,MAAMJ,mBAAmBK,OAAAA,OAAK,CAACC,aAAa,CACjD;AAEK,MAAMJ,sBAAsBG,OAAAA,OAAK,CAACC,aAAa,CAK5C;AAEH,MAAML,4BAA4BI,OAAAA,OAAK,CAACC,aAAa,CAIzD;AAEI,MAAMF,kBAAkBC,OAAAA,OAAK,CAACC,aAAa,CAAkB;AAEpE,wCAA2C;IACzCN,iBAAiBU,WAAW,GAAG;IAC/BR,oBAAoBQ,WAAW,GAAG;IAClCT,0BAA0BS,WAAW,GAAG;IACxCN,gBAAgBM,WAAW,GAAG;AAChC;AAEO,MAAMP,qBAAqBE,OAAAA,OAAK,CAACC,aAAa,CAAc,IAAIK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\nexport const HTML_LIMITED_BOT_UA_RE =\n  /Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;;;;;+BACzJA,0BAAAA;;;eAAAA;;;AAAN,MAAMA,yBACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS\nconst HEADLESS_BROWSER_BOT_UA_RE =\n  /Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "getBotType", "isBot", "HEADLESS_BROWSER_BOT_UA_RE", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;IAQSA,sBAAsB,EAAA;eAAtBA,UAAAA,sBAAsB;;IAFlBC,6BAA6B,EAAA;eAA7BA;;IAgBGC,UAAU,EAAA;eAAVA;;IAJAC,KAAK,EAAA;eAALA;;;0BAlBuB;AAEvC,kEAAkE;AAClE,MAAMC,6BACJ;AAEK,MAAMH,gCAAgCD,UAAAA,sBAAsB,CAACK,MAAM;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOH,2BAA2BI,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,OAAOP,UAAAA,sBAAsB,CAACQ,IAAI,CAACD;AACrC;AAEO,SAASJ,MAAMI,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASL,WAAWK,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOG;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC,GAAA;;;;+BACeA,aAAAA;;;eAAAA;;;AAAT,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAMgBA,iBAAAA;;;eAAAA;;;2BANU;AAMnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACeA,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/server-inserted-html.shared-runtime.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext } from 'react'\n\nexport type ServerInsertedHTMLHook = (callbacks: () => React.ReactNode) => void\n\n// Use `React.createContext` to avoid errors from the RSC checks because\n// it can't be imported directly in Server Components:\n//\n//   import { createContext } from 'react'\n//\n// More info: https://github.com/vercel/next.js/pull/40686\nexport const ServerInsertedHTMLContext =\n  React.createContext<ServerInsertedHTMLHook | null>(null as any)\n\nexport function useServerInsertedHTML(callback: () => React.ReactNode): void {\n  const addInsertedServerHTMLCallback = useContext(ServerInsertedHTMLContext)\n  // Should have no effects on client where there's no flush effects provider\n  if (addInsertedServerHTMLCallback) {\n    addInsertedServerHTMLCallback(callback)\n  }\n}\n"], "names": ["ServerInsertedHTMLContext", "useServerInsertedHTML", "React", "createContext", "callback", "addInsertedServerHTMLCallback", "useContext"], "mappings": "AAAA;;;;;;;;;;;;;;;;IAYaA,yBAAyB,EAAA;eAAzBA;;IAGGC,qBAAqB,EAAA;eAArBA;;;;iEAbkB;AAU3B,MAAMD,4BAAAA,WAAAA,GACXE,OAAAA,OAAK,CAACC,aAAa,CAAgC;AAE9C,SAASF,sBAAsBG,QAA+B;IACnE,MAAMC,gCAAgCC,CAAAA,GAAAA,OAAAA,UAAU,EAACN;IACjD,2EAA2E;IAC3E,IAAIK,+BAA+B;QACjCA,8BAA8BD;IAChC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/router/utils/path-has-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n"], "names": ["pathHasPrefix", "path", "prefix", "pathname", "parsePath", "startsWith"], "mappings": ";;;;+BASgBA,iBAAAA;;;eAAAA;;;2BATU;AASnB,SAASA,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACH;IAC/B,OAAOE,aAAaD,UAAUC,SAASE,UAAU,CAACH,SAAS;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,YAAAA;;;eAAAA;;;AAXT,IAAIA,WAAW,CAACC,KAAe;AAC/B,wCAA2C;IACzC,MAAMI,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1083, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/magic-identifier.ts"], "sourcesContent": ["function decodeHex(hexStr: string): string {\n  if (hexStr.trim() === '') {\n    throw new Error(\"can't decode empty hex\")\n  }\n\n  const num = parseInt(hexStr, 16)\n  if (isNaN(num)) {\n    throw new Error(`invalid hex: \\`${hexStr}\\``)\n  }\n\n  return String.fromCodePoint(num)\n}\n\nconst enum Mode {\n  Text,\n  Underscore,\n  Hex,\n  LongHex,\n}\n\nconst DECODE_REGEX = /^__TURBOPACK__([a-zA-Z0-9_$]+)__$/\n\nexport function decodeMagicIdentifier(identifier: string): string {\n  const matches = identifier.match(DECODE_REGEX)\n  if (!matches) {\n    return identifier\n  }\n\n  const inner = matches[1]\n\n  let output = ''\n\n  let mode: Mode = Mode.Text\n  let buffer = ''\n  for (let i = 0; i < inner.length; i++) {\n    const char = inner[i]\n\n    if (mode === Mode.Text) {\n      if (char === '_') {\n        mode = Mode.Underscore\n      } else if (char === '$') {\n        mode = Mode.Hex\n      } else {\n        output += char\n      }\n    } else if (mode === Mode.Underscore) {\n      if (char === '_') {\n        output += ' '\n        mode = Mode.Text\n      } else if (char === '$') {\n        output += '_'\n        mode = Mode.Hex\n      } else {\n        output += char\n        mode = Mode.Text\n      }\n    } else if (mode === Mode.Hex) {\n      if (buffer.length === 2) {\n        output += decodeHex(buffer)\n        buffer = ''\n      }\n\n      if (char === '_') {\n        if (buffer !== '') {\n          throw new Error(`invalid hex: \\`${buffer}\\``)\n        }\n\n        mode = Mode.LongHex\n      } else if (char === '$') {\n        if (buffer !== '') {\n          throw new Error(`invalid hex: \\`${buffer}\\``)\n        }\n\n        mode = Mode.Text\n      } else {\n        buffer += char\n      }\n    } else if (mode === Mode.LongHex) {\n      if (char === '_') {\n        throw new Error(`invalid hex: \\`${buffer + char}\\``)\n      } else if (char === '$') {\n        output += decodeHex(buffer)\n        buffer = ''\n\n        mode = Mode.Text\n      } else {\n        buffer += char\n      }\n    }\n  }\n\n  return output\n}\n\nexport const MAGIC_IDENTIFIER_REGEX = /__TURBOPACK__[a-zA-Z0-9_$]+__/g\n"], "names": ["MAGIC_IDENTIFIER_REGEX", "decodeMagicIdentifier", "decodeHex", "hexStr", "trim", "Error", "num", "parseInt", "isNaN", "String", "fromCodePoint", "DECODE_REGEX", "identifier", "matches", "match", "inner", "output", "mode", "buffer", "i", "length", "char"], "mappings": ";;;;;;;;;;;;;;;IA8FaA,sBAAsB,EAAA;eAAtBA;;IAxEGC,qBAAqB,EAAA;eAArBA;;;AAtBhB,SAASC,UAAUC,MAAc;IAC/B,IAAIA,OAAOC,IAAI,OAAO,IAAI;QACxB,MAAM,OAAA,cAAmC,CAAnC,IAAIC,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,MAAMC,MAAMC,SAASJ,QAAQ;IAC7B,IAAIK,MAAMF,MAAM;QACd,MAAM,OAAA,cAAuC,CAAvC,IAAID,MAAO,mBAAiBF,SAAO,MAAnC,qBAAA;mBAAA;wBAAA;0BAAA;QAAsC;IAC9C;IAEA,OAAOM,OAAOC,aAAa,CAACJ;AAC9B;AASA,MAAMK,eAAe;AAEd,SAASV,sBAAsBW,UAAkB;IACtD,MAAMC,UAAUD,WAAWE,KAAK,CAACH;IACjC,IAAI,CAACE,SAAS;QACZ,OAAOD;IACT;IAEA,MAAMG,QAAQF,OAAO,CAAC,EAAE;IAExB,IAAIG,SAAS;IAEb,IAAIC,OAAAA;IACJ,IAAIC,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,MAAMK,MAAM,EAAED,IAAK;QACrC,MAAME,OAAON,KAAK,CAACI,EAAE;QAErB,IAAIF,SAAAA,GAAoB;YACtB,IAAII,SAAS,KAAK;gBAChBJ,OAAAA;YACF,OAAO,IAAII,SAAS,KAAK;gBACvBJ,OAAAA;YACF,OAAO;gBACLD,UAAUK;YACZ;QACF,OAAO,IAAIJ,SAAAA,GAA0B;YACnC,IAAII,SAAS,KAAK;gBAChBL,UAAU;gBACVC,OAAAA;YACF,OAAO,IAAII,SAAS,KAAK;gBACvBL,UAAU;gBACVC,OAAAA;YACF,OAAO;gBACLD,UAAUK;gBACVJ,OAAAA;YACF;QACF,OAAO,IAAIA,SAAAA,GAAmB;YAC5B,IAAIC,OAAOE,MAAM,KAAK,GAAG;gBACvBJ,UAAUd,UAAUgB;gBACpBA,SAAS;YACX;YAEA,IAAIG,SAAS,KAAK;gBAChB,IAAIH,WAAW,IAAI;oBACjB,MAAM,OAAA,cAAuC,CAAvC,IAAIb,MAAO,mBAAiBa,SAAO,MAAnC,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsC;gBAC9C;gBAEAD,OAAAA;YACF,OAAO,IAAII,SAAS,KAAK;gBACvB,IAAIH,WAAW,IAAI;oBACjB,MAAM,OAAA,cAAuC,CAAvC,IAAIb,MAAO,mBAAiBa,SAAO,MAAnC,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsC;gBAC9C;gBAEAD,OAAAA;YACF,OAAO;gBACLC,UAAUG;YACZ;QACF,OAAO,IAAIJ,SAAAA,GAAuB;YAChC,IAAII,SAAS,KAAK;gBAChB,MAAM,OAAA,cAA8C,CAA9C,IAAIhB,MAAO,mBAAiBa,CAAAA,SAASG,IAAG,IAAE,MAA1C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6C;YACrD,OAAO,IAAIA,SAAS,KAAK;gBACvBL,UAAUd,UAAUgB;gBACpBA,SAAS;gBAETD,OAAAA;YACF,OAAO;gBACLC,UAAUG;YACZ;QACF;IACF;IAEA,OAAOL;AACT;AAEO,MAAMhB,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/error-source.ts"], "sourcesContent": ["const symbolError = Symbol.for('NextjsError')\n\nexport function getErrorSource(error: Error): 'server' | 'edge-server' | null {\n  return (error as any)[symbolError] || null\n}\n\nexport type ErrorSourceType = 'edge-server' | 'server'\n\nexport function decorateServerError(error: Error, type: ErrorSourceType) {\n  Object.defineProperty(error, symbolError, {\n    writable: false,\n    enumerable: false,\n    configurable: false,\n    value: type,\n  })\n}\n"], "names": ["decorateServerError", "getErrorSource", "symbolError", "Symbol", "for", "error", "type", "Object", "defineProperty", "writable", "enumerable", "configurable", "value"], "mappings": ";;;;;;;;;;;;;;;IAQgBA,mBAAmB,EAAA;eAAnBA;;IANAC,cAAc,EAAA;eAAdA;;;AAFhB,MAAMC,cAAcC,OAAOC,GAAG,CAAC;AAExB,SAASH,eAAeI,KAAY;IACzC,OAAQA,KAAa,CAACH,YAAY,IAAI;AACxC;AAIO,SAASF,oBAAoBK,KAAY,EAAEC,IAAqB;IACrEC,OAAOC,cAAc,CAACH,OAAOH,aAAa;QACxCO,UAAU;QACVC,YAAY;QACZC,cAAc;QACdC,OAAON;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/errors/constants.ts"], "sourcesContent": ["export const MISSING_ROOT_TAGS_ERROR = 'NEXT_MISSING_ROOT_TAGS'\n"], "names": ["MISSING_ROOT_TAGS_ERROR"], "mappings": ";;;;+BAAaA,2BAAAA;;;eAAAA;;;AAAN,MAAMA,0BAA0B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/normalized-asset-prefix.ts"], "sourcesContent": ["export function normalizedAssetPrefix(assetPrefix: string | undefined): string {\n  // remove all leading slashes and trailing slashes\n  const escapedAssetPrefix = assetPrefix?.replace(/^\\/+|\\/+$/g, '') || false\n\n  // if an assetPrefix was '/', we return empty string\n  // because it could be an unnecessary trailing slash\n  if (!escapedAssetPrefix) {\n    return ''\n  }\n\n  if (URL.canParse(escapedAssetPrefix)) {\n    const url = new URL(escapedAssetPrefix).toString()\n    return url.endsWith('/') ? url.slice(0, -1) : url\n  }\n\n  // assuming assetPrefix here is a pathname-style,\n  // restore the leading slash\n  return `/${escapedAssetPrefix}`\n}\n"], "names": ["normalizedAssetPrefix", "assetPrefix", "escapedAssetPrefix", "replace", "URL", "canParse", "url", "toString", "endsWith", "slice"], "mappings": ";;;;+BAAgBA,yBAAAA;;;eAAAA;;;AAAT,SAASA,sBAAsBC,WAA+B;IACnE,kDAAkD;IAClD,MAAMC,qBAAqBD,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAaE,OAAO,CAAC,cAAc,GAAA,KAAO;IAErE,oDAAoD;IACpD,oDAAoD;IACpD,IAAI,CAACD,oBAAoB;QACvB,OAAO;IACT;IAEA,IAAIE,IAAIC,QAAQ,CAACH,qBAAqB;QACpC,MAAMI,MAAM,IAAIF,IAAIF,oBAAoBK,QAAQ;QAChD,OAAOD,IAAIE,QAAQ,CAAC,OAAOF,IAAIG,KAAK,CAAC,GAAG,CAAC,KAAKH;IAChD;IAEA,iDAAiD;IACjD,4BAA4B;IAC5B,OAAQ,MAAGJ;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/shared/lib/server-reference-info.ts"], "sourcesContent": ["export interface ServerReferenceInfo {\n  type: 'server-action' | 'use-cache'\n  usedArgs: [boolean, boolean, boolean, boolean, boolean, boolean]\n  hasRestArgs: boolean\n}\n\n/**\n * Extracts info about the server reference for the given server reference ID by\n * parsing the first byte of the hex-encoded ID.\n *\n * ```\n * Bit positions: [7]      [6] [5] [4] [3] [2] [1]  [0]\n * Bits:          typeBit  argMask                  restArgs\n * ```\n *\n * If the `typeBit` is `1` the server reference represents a `\"use cache\"`\n * function, otherwise a server action.\n *\n * The `argMask` encodes whether the function uses the argument at the\n * respective position.\n *\n * The `restArgs` bit indicates whether the function uses a rest parameter. It's\n * also set to 1 if the function has more than 6 args.\n *\n * @param id hex-encoded server reference ID\n */\nexport function extractInfoFromServerReferenceId(\n  id: string\n): ServerReferenceInfo {\n  const infoByte = parseInt(id.slice(0, 2), 16)\n  const typeBit = (infoByte >> 7) & 0x1\n  const argMask = (infoByte >> 1) & 0x3f\n  const restArgs = infoByte & 0x1\n  const usedArgs = Array(6)\n\n  for (let index = 0; index < 6; index++) {\n    const bitPosition = 5 - index\n    const bit = (argMask >> bitPosition) & 0x1\n    usedArgs[index] = bit === 1\n  }\n\n  return {\n    type: typeBit === 1 ? 'use-cache' : 'server-action',\n    usedArgs: usedArgs as [\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n    ],\n    hasRestArgs: restArgs === 1,\n  }\n}\n\n/**\n * Creates a sparse array containing only the used arguments based on the\n * provided action info.\n */\nexport function omitUnusedArgs(\n  args: unknown[],\n  info: ServerReferenceInfo\n): unknown[] {\n  const filteredArgs = new Array(args.length)\n\n  for (let index = 0; index < args.length; index++) {\n    if (\n      (index < 6 && info.usedArgs[index]) ||\n      // This assumes that the server reference info byte has the restArgs bit\n      // set to 1 if there are more than 6 args.\n      (index >= 6 && info.hasRestArgs)\n    ) {\n      filteredArgs[index] = args[index]\n    }\n  }\n\n  return filteredArgs\n}\n"], "names": ["extractInfoFromServerReferenceId", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "infoByte", "parseInt", "slice", "typeBit", "argMask", "restArgs", "usedArgs", "Array", "index", "bitPosition", "bit", "type", "hasRestArgs", "args", "info", "filteredArgs", "length"], "mappings": ";;;;;;;;;;;;;;;IA0BgBA,gCAAgC,EAAA;eAAhCA;;IAiCAC,cAAc,EAAA;eAAdA;;;AAjCT,SAASD,iCACdE,EAAU;IAEV,MAAMC,WAAWC,SAASF,GAAGG,KAAK,CAAC,GAAG,IAAI;IAC1C,MAAMC,UAAWH,YAAY,IAAK;IAClC,MAAMI,UAAWJ,YAAY,IAAK;IAClC,MAAMK,WAAWL,WAAW;IAC5B,MAAMM,WAAWC,MAAM;IAEvB,IAAK,IAAIC,QAAQ,GAAGA,QAAQ,GAAGA,QAAS;QACtC,MAAMC,cAAc,IAAID;QACxB,MAAME,MAAON,WAAWK,cAAe;QACvCH,QAAQ,CAACE,MAAM,GAAGE,QAAQ;IAC5B;IAEA,OAAO;QACLC,MAAMR,YAAY,IAAI,cAAc;QACpCG,UAAUA;QAQVM,aAAaP,aAAa;IAC5B;AACF;AAMO,SAASP,eACde,IAAe,EACfC,IAAyB;IAEzB,MAAMC,eAAe,IAAIR,MAAMM,KAAKG,MAAM;IAE1C,IAAK,IAAIR,QAAQ,GAAGA,QAAQK,KAAKG,MAAM,EAAER,QAAS;QAChD,IACGA,QAAQ,KAAKM,KAAKR,QAAQ,CAACE,MAAM,IAClC,wEAAwE;QACxE,0CAA0C;QACzCA,SAAS,KAAKM,KAAKF,WAAW,EAC/B;YACAG,YAAY,CAACP,MAAM,GAAGK,IAAI,CAACL,MAAM;QACnC;IACF;IAEA,OAAOO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/app-render/async-local-storage.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\nconst sharedAsyncLocalStorageNotAvailableError = new Error(\n  'Invariant: AsyncLocalStorage accessed in runtime where it is not available'\n)\n\nclass FakeAsyncLocalStorage<Store extends {}>\n  implements AsyncLocalStorage<Store>\n{\n  disable(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  getStore(): Store | undefined {\n    // This fake implementation of AsyncLocalStorage always returns `undefined`.\n    return undefined\n  }\n\n  run<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  exit<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  enterWith(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  static bind<T>(fn: T): T {\n    return fn\n  }\n}\n\nconst maybeGlobalAsyncLocalStorage =\n  typeof globalThis !== 'undefined' && (globalThis as any).AsyncLocalStorage\n\nexport function createAsyncLocalStorage<\n  Store extends {},\n>(): AsyncLocalStorage<Store> {\n  if (maybeGlobalAsyncLocalStorage) {\n    return new maybeGlobalAsyncLocalStorage()\n  }\n  return new FakeAsyncLocalStorage()\n}\n\nexport function bindSnapshot<T>(fn: T): T {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.bind(fn)\n  }\n  return FakeAsyncLocalStorage.bind(fn)\n}\n\nexport function createSnapshot(): <R, TArgs extends any[]>(\n  fn: (...args: TArgs) => R,\n  ...args: TArgs\n) => R {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.snapshot()\n  }\n  return function (fn: any, ...args: any[]) {\n    return fn(...args)\n  }\n}\n"], "names": ["bindSnapshot", "createAsyncLocalStorage", "createSnapshot", "sharedAsyncLocalStorageNotAvailableError", "Error", "FakeAsyncLocalStorage", "disable", "getStore", "undefined", "run", "exit", "enterWith", "bind", "fn", "maybeGlobalAsyncLocalStorage", "globalThis", "AsyncLocalStorage", "snapshot", "args"], "mappings": ";;;;;;;;;;;;;;;;IA+CgBA,YAAY,EAAA;eAAZA;;IATAC,uBAAuB,EAAA;eAAvBA;;IAgBAC,cAAc,EAAA;eAAdA;;;AApDhB,MAAMC,2CAA2C,OAAA,cAEhD,CAFgD,IAAIC,MACnD,+EAD+C,qBAAA;WAAA;gBAAA;kBAAA;AAEjD;AAEA,MAAMC;IAGJC,UAAgB;QACd,MAAMH;IACR;IAEAI,WAA8B;QAC5B,4EAA4E;QAC5E,OAAOC;IACT;IAEAC,MAAY;QACV,MAAMN;IACR;IAEAO,OAAa;QACX,MAAMP;IACR;IAEAQ,YAAkB;QAChB,MAAMR;IACR;IAEA,OAAOS,KAAQC,EAAK,EAAK;QACvB,OAAOA;IACT;AACF;AAEA,MAAMC,+BACJ,OAAOC,eAAe,eAAgBA,WAAmBC,iBAAiB;AAErE,SAASf;IAGd,IAAIa,8BAA8B;QAChC,OAAO,IAAIA;IACb;IACA,OAAO,IAAIT;AACb;AAEO,SAASL,aAAgBa,EAAK;IACnC,IAAIC,8BAA8B;QAChC,OAAOA,6BAA6BF,IAAI,CAACC;IAC3C;IACA,OAAOR,sBAAsBO,IAAI,CAACC;AACpC;AAEO,SAASX;IAId,IAAIY,8BAA8B;QAChC,OAAOA,6BAA6BG,QAAQ;IAC9C;IACA,OAAO,SAAUJ,EAAO,EAAE,GAAGK,IAAW;QACtC,OAAOL,MAAMK;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1430, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/app-render/work-async-storage-instance.ts"], "sourcesContent": ["import type { WorkAsyncStorage } from './work-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const workAsyncStorageInstance: WorkAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["workAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA,4BAAAA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,2BACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1447, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/app-render/work-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { FetchMetrics } from '../base-http'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\nimport type { AfterContext } from '../after/after-context'\nimport type { CacheLife } from '../use-cache/cache-life'\n\n// Share the instance module in the next-shared layer\nimport { workAsyncStorageInstance } from './work-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { LazyResult } from '../lib/lazy-result'\n\nexport interface WorkStore {\n  readonly isStaticGeneration: boolean\n\n  /**\n   * The page that is being rendered. This relates to the path to the page file.\n   */\n  readonly page: string\n\n  /**\n   * The route that is being rendered. This is the page property without the\n   * trailing `/page` or `/route` suffix.\n   */\n  readonly route: string\n\n  /**\n   * The set of unknown route parameters. Accessing these will be tracked as\n   * a dynamic access.\n   */\n  readonly fallbackRouteParams: FallbackRouteParams | null\n\n  readonly incrementalCache?: IncrementalCache\n  readonly cacheLifeProfiles?: { [profile: string]: CacheLife }\n\n  readonly isOnDemandRevalidate?: boolean\n  readonly isPrerendering?: boolean\n  readonly isRevalidate?: boolean\n\n  forceDynamic?: boolean\n  fetchCache?: AppSegmentConfig['fetchCache']\n\n  forceStatic?: boolean\n  dynamicShouldError?: boolean\n  pendingRevalidates?: Record<string, Promise<any>>\n  pendingRevalidateWrites?: Array<Promise<void>> // This is like pendingRevalidates but isn't used for deduping.\n  readonly afterContext: AfterContext\n\n  dynamicUsageDescription?: string\n  dynamicUsageStack?: string\n\n  /**\n   * Invalid usage errors might be caught in userland. We attach them to the\n   * work store to ensure we can still fail the build or dev render.\n   */\n  // TODO: Collect an array of errors, and throw as AggregateError when\n  // `serializeError` and the Dev Overlay support it.\n  invalidUsageError?: Error\n\n  nextFetchId?: number\n  pathWasRevalidated?: boolean\n\n  /**\n   * Tags that were revalidated during the current request. They need to be sent\n   * to cache handlers to propagate their revalidation.\n   */\n  pendingRevalidatedTags?: string[]\n\n  /**\n   * Tags that were previously revalidated (e.g. by a redirecting server action)\n   * and have already been sent to cache handlers. Retrieved cache entries that\n   * include any of these tags must be discarded.\n   */\n  readonly previouslyRevalidatedTags: readonly string[]\n\n  /**\n   * This map contains lazy results so that we can evaluate them when the first\n   * cache entry is read. It allows us to skip refreshing tags if no caches are\n   * read at all.\n   */\n  readonly refreshTagsByCacheKind: Map<string, LazyResult<void>>\n\n  fetchMetrics?: FetchMetrics\n\n  isDraftMode?: boolean\n  isUnstableNoStore?: boolean\n  isPrefetchRequest?: boolean\n\n  requestEndedState?: { ended?: boolean }\n\n  buildId: string\n\n  readonly reactLoadableManifest?: DeepReadonly<\n    Record<string, { files: string[] }>\n  >\n  readonly assetPrefix?: string\n\n  dynamicIOEnabled: boolean\n  dev: boolean\n}\n\nexport type WorkAsyncStorage = AsyncLocalStorage<WorkStore>\n\nexport { workAsyncStorageInstance as workAsyncStorage }\n"], "names": ["workAsyncStorage", "workAsyncStorageInstance"], "mappings": ";;;;+BAwGqCA,oBAAAA;;;eAA5BC,0BAAAA,wBAAwB;;;0CA9FQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/app-render/action-async-storage-instance.ts"], "sourcesContent": ["import type { ActionAsyncStorage } from './action-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const actionAsyncStorageInstance: ActionAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["actionAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA,8BAAAA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,6BACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1480, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/app-render/action-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { actionAsyncStorageInstance } from './action-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nexport interface ActionStore {\n  readonly isAction?: boolean\n  readonly isAppRoute?: boolean\n}\n\nexport type ActionAsyncStorage = AsyncLocalStorage<ActionStore>\n\nexport { actionAsyncStorageInstance as actionAsyncStorage }\n"], "names": ["actionAsyncStorage", "actionAsyncStorageInstance"], "mappings": ";;;;+BAWuCA,sBAAAA;;;eAA9BC,4BAAAA,0BAA0B;;;4CARQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IAgCAC,kBAAkB,EAAA;eAAlBA;;;AAhCT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIT,6BAA6BG;IACzD,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIb,6BAA6BG;YAEnC,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1567, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1586, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/app-render/work-unit-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { WorkUnitAsyncStorage } from './work-unit-async-storage.external'\n\nexport const workUnitAsyncStorageInstance: WorkUnitAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["workUnitAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA,gCAAAA;;;eAAAA;;;mCAH2B;AAGjC,MAAMA,+BACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/app-render/work-unit-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\nimport type { ResponseCookies } from '../web/spec-extension/cookies'\nimport type { ReadonlyHeaders } from '../web/spec-extension/adapters/headers'\nimport type { ReadonlyRequestCookies } from '../web/spec-extension/adapters/request-cookies'\nimport type { CacheSignal } from './cache-signal'\nimport type { DynamicTrackingState } from './dynamic-rendering'\n\n// Share the instance module in the next-shared layer\nimport { workUnitAsyncStorageInstance } from './work-unit-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type {\n  RenderResumeDataCache,\n  PrerenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { Params } from '../request/params'\nimport type { ImplicitTags } from '../lib/implicit-tags'\nimport type { WorkStore } from './work-async-storage.external'\nimport { NEXT_HMR_REFRESH_HASH_COOKIE } from '../../client/components/app-router-headers'\n\nexport type WorkUnitPhase = 'action' | 'render' | 'after'\n\nexport interface CommonWorkUnitStore {\n  /** NOTE: Will be mutated as phases change */\n  phase: WorkUnitPhase\n  readonly implicitTags: ImplicitTags\n}\n\nexport interface RequestStore extends CommonWorkUnitStore {\n  type: 'request'\n\n  /**\n   * The URL of the request. This only specifies the pathname and the search\n   * part of the URL.\n   */\n  readonly url: {\n    /**\n     * The pathname of the requested URL.\n     */\n    readonly pathname: string\n\n    /**\n     * The search part of the requested URL. If the request did not provide a\n     * search part, this will be an empty string.\n     */\n    readonly search: string\n  }\n\n  readonly headers: ReadonlyHeaders\n  // This is mutable because we need to reassign it when transitioning from the action phase to the render phase.\n  // The cookie object itself is deliberately read only and thus can't be updated.\n  cookies: ReadonlyRequestCookies\n  readonly mutableCookies: ResponseCookies\n  readonly userspaceMutableCookies: ResponseCookies\n  readonly draftMode: DraftModeProvider\n  readonly isHmrRefresh?: boolean\n  readonly serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  readonly rootParams: Params\n\n  /**\n   * The resume data cache for this request. This will be a immutable cache.\n   */\n  renderResumeDataCache: RenderResumeDataCache | null\n\n  // DEV-only\n  usedDynamic?: boolean\n  prerenderPhase?: boolean\n}\n\n/**\n * The Prerender store is for tracking information related to prerenders.\n *\n * It can be used for both RSC and SSR prerendering and should be scoped as close\n * to the individual `renderTo...` API call as possible. To keep the type simple\n * we don't distinguish between RSC and SSR prerendering explicitly but instead\n * use conditional object properties to infer which mode we are in. For instance cache tracking\n * only needs to happen during the RSC prerender when we are prospectively prerendering\n * to fill all caches.\n */\nexport interface PrerenderStoreModern extends CommonWorkUnitStore {\n  type: 'prerender'\n\n  /**\n   * This signal is aborted when the React render is complete. (i.e. it is the same signal passed to react)\n   */\n  readonly renderSignal: AbortSignal\n  /**\n   * This is the AbortController which represents the boundary between Prerender and dynamic. In some renders it is\n   * the same as the controller for the renderSignal but in others it is a separate controller. It should be aborted\n   * whenever the we are no longer in the prerender phase of rendering. Typically this is after one task or when you call\n   * a sync API which requires the prerender to end immediately\n   */\n  readonly controller: AbortController\n\n  /**\n   * when not null this signal is used to track cache reads during prerendering and\n   * to await all cache reads completing before aborting the prerender.\n   */\n  readonly cacheSignal: null | CacheSignal\n\n  /**\n   * During some prerenders we want to track dynamic access.\n   */\n  readonly dynamicTracking: null | DynamicTrackingState\n\n  readonly rootParams: Params\n\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n\n  /**\n   * The resume data cache for this prerender.\n   */\n  prerenderResumeDataCache: PrerenderResumeDataCache | null\n\n  // DEV ONLY\n  // When used this flag informs certain APIs to skip logging because we're\n  // not part of the primary render path and are just prerendering to produce\n  // validation results\n  validating?: boolean\n\n  /**\n   * The HMR refresh hash is only provided in dev mode. It is needed for the dev\n   * warmup render to ensure that the cache keys will be identical for the\n   * subsequent dynamic render.\n   */\n  readonly hmrRefreshHash: string | undefined\n}\n\nexport interface PrerenderStorePPR extends CommonWorkUnitStore {\n  type: 'prerender-ppr'\n  readonly rootParams: Params\n  readonly dynamicTracking: null | DynamicTrackingState\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n\n  /**\n   * The resume data cache for this prerender.\n   */\n  prerenderResumeDataCache: PrerenderResumeDataCache\n}\n\nexport interface PrerenderStoreLegacy extends CommonWorkUnitStore {\n  type: 'prerender-legacy'\n  readonly rootParams: Params\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n}\n\nexport type PrerenderStore =\n  | PrerenderStoreLegacy\n  | PrerenderStorePPR\n  | PrerenderStoreModern\n\nexport interface CommonCacheStore\n  extends Omit<CommonWorkUnitStore, 'implicitTags'> {\n  /**\n   * A cache work unit store might not always have an outer work unit store,\n   * from which implicit tags could be inherited.\n   */\n  readonly implicitTags: ImplicitTags | undefined\n}\n\nexport interface UseCacheStore extends CommonCacheStore {\n  type: 'cache'\n  // Collected revalidate times and tags for this cache entry during the cache render.\n  revalidate: number // implicit revalidate time from inner caches / fetches\n  expire: number // server expiration time\n  stale: number // client expiration time\n  explicitRevalidate: undefined | number // explicit revalidate time from cacheLife() calls\n  explicitExpire: undefined | number // server expiration time\n  explicitStale: undefined | number // client expiration time\n  tags: null | string[]\n  readonly hmrRefreshHash: string | undefined\n  readonly isHmrRefresh: boolean\n  readonly serverComponentsHmrCache: ServerComponentsHmrCache | undefined\n  readonly forceRevalidate: boolean\n  // Draft mode is only available if the outer work unit store is a request\n  // store and draft mode is enabled.\n  readonly draftMode: DraftModeProvider | undefined\n}\n\nexport interface UnstableCacheStore extends CommonCacheStore {\n  type: 'unstable-cache'\n  // Draft mode is only available if the outer work unit store is a request\n  // store and draft mode is enabled.\n  readonly draftMode: DraftModeProvider | undefined\n}\n\n/**\n * The Cache store is for tracking information inside a \"use cache\" or unstable_cache context.\n * Inside this context we should never expose any request or page specific information.\n */\nexport type CacheStore = UseCacheStore | UnstableCacheStore\n\nexport type WorkUnitStore = RequestStore | CacheStore | PrerenderStore\n\nexport type WorkUnitAsyncStorage = AsyncLocalStorage<WorkUnitStore>\n\nexport { workUnitAsyncStorageInstance as workUnitAsyncStorage }\n\nexport function getExpectedRequestStore(\n  callingExpression: string\n): RequestStore {\n  const workUnitStore = workUnitAsyncStorageInstance.getStore()\n\n  if (!workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'request':\n      return workUnitStore\n\n    case 'prerender':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // This should not happen because we should have checked it already.\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside a prerender. This is a bug in Next.js.`\n      )\n\n    case 'cache':\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside \"use cache\". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`\n      )\n\n    case 'unstable-cache':\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n      )\n\n    default:\n      const _exhaustiveCheck: never = workUnitStore\n      return _exhaustiveCheck\n  }\n}\n\nexport function throwForMissingRequestStore(callingExpression: string): never {\n  throw new Error(\n    `\\`${callingExpression}\\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`\n  )\n}\n\nexport function getPrerenderResumeDataCache(\n  workUnitStore: WorkUnitStore\n): PrerenderResumeDataCache | null {\n  if (\n    workUnitStore.type === 'prerender' ||\n    workUnitStore.type === 'prerender-ppr'\n  ) {\n    return workUnitStore.prerenderResumeDataCache\n  }\n\n  return null\n}\n\nexport function getRenderResumeDataCache(\n  workUnitStore: WorkUnitStore\n): RenderResumeDataCache | null {\n  if (\n    workUnitStore.type !== 'prerender-legacy' &&\n    workUnitStore.type !== 'cache' &&\n    workUnitStore.type !== 'unstable-cache'\n  ) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.renderResumeDataCache\n    }\n\n    // We return the mutable resume data cache here as an immutable version of\n    // the cache as it can also be used for reading.\n    return workUnitStore.prerenderResumeDataCache\n  }\n\n  return null\n}\n\nexport function getHmrRefreshHash(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore\n): string | undefined {\n  if (!workStore.dev) {\n    return undefined\n  }\n\n  return workUnitStore.type === 'cache' || workUnitStore.type === 'prerender'\n    ? workUnitStore.hmrRefreshHash\n    : workUnitStore.type === 'request'\n      ? workUnitStore.cookies.get(NEXT_HMR_REFRESH_HASH_COOKIE)?.value\n      : undefined\n}\n\n/**\n * Returns a draft mode provider only if draft mode is enabled.\n */\nexport function getDraftModeProviderForCacheScope(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore\n): DraftModeProvider | undefined {\n  if (workStore.isDraftMode) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n      case 'request':\n        return workUnitStore.draftMode\n      default:\n        return undefined\n    }\n  }\n\n  return undefined\n}\n"], "names": ["getDraftModeProviderForCacheScope", "getExpectedRequestStore", "getHmrRefreshHash", "getPrerenderResumeDataCache", "getRenderResumeDataCache", "throwForMissingRequestStore", "workUnitAsyncStorage", "workUnitAsyncStorageInstance", "callingExpression", "workUnitStore", "getStore", "type", "Error", "_exhaustiveCheck", "prerenderResumeDataCache", "renderResumeDataCache", "workStore", "dev", "undefined", "hmrRefreshHash", "cookies", "get", "NEXT_HMR_REFRESH_HASH_COOKIE", "value", "isDraftMode", "draftMode"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAiTgBA,iCAAiC,EAAA;eAAjCA;;IA9FAC,uBAAuB,EAAA;eAAvBA;;IA4EAC,iBAAiB,EAAA;eAAjBA;;IAjCAC,2BAA2B,EAAA;eAA3BA;;IAaAC,wBAAwB,EAAA;eAAxBA;;IAnBAC,2BAA2B,EAAA;eAA3BA;;IAvCyBC,oBAAoB,EAAA;eAApDC,8BAAAA,4BAA4B;;;8CAxMQ;kCASA;AAiMtC,SAASN,wBACdO,iBAAyB;IAEzB,MAAMC,gBAAgBF,8BAAAA,4BAA4B,CAACG,QAAQ;IAE3D,IAAI,CAACD,eAAe;QAClBJ,4BAA4BG;IAC9B;IAEA,OAAQC,cAAcE,IAAI;QACxB,KAAK;YACH,OAAOF;QAET,KAAK;QACL,KAAK;QACL,KAAK;YACH,oEAAoE;YACpE,MAAM,OAAA,cAEL,CAFK,IAAIG,MACR,CAAC,EAAE,EAAEJ,kBAAkB,iEAAiE,CAAC,GADrF,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QAEF,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAII,MACR,CAAC,EAAE,EAAEJ,kBAAkB,2JAA2J,CAAC,GAD/K,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QAEF,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAII,MACR,CAAC,EAAE,EAAEJ,kBAAkB,sKAAsK,CAAC,GAD1L,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QAEF;YACE,MAAMK,mBAA0BJ;YAChC,OAAOI;IACX;AACF;AAEO,SAASR,4BAA4BG,iBAAyB;IACnE,MAAM,OAAA,cAEL,CAFK,IAAII,MACR,CAAC,EAAE,EAAEJ,kBAAkB,iHAAiH,CAAC,GADrI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASL,4BACdM,aAA4B;IAE5B,IACEA,cAAcE,IAAI,KAAK,eACvBF,cAAcE,IAAI,KAAK,iBACvB;QACA,OAAOF,cAAcK,wBAAwB;IAC/C;IAEA,OAAO;AACT;AAEO,SAASV,yBACdK,aAA4B;IAE5B,IACEA,cAAcE,IAAI,KAAK,sBACvBF,cAAcE,IAAI,KAAK,WACvBF,cAAcE,IAAI,KAAK,kBACvB;QACA,IAAIF,cAAcE,IAAI,KAAK,WAAW;YACpC,OAAOF,cAAcM,qBAAqB;QAC5C;QAEA,0EAA0E;QAC1E,gDAAgD;QAChD,OAAON,cAAcK,wBAAwB;IAC/C;IAEA,OAAO;AACT;AAEO,SAASZ,kBACdc,SAAoB,EACpBP,aAA4B;QAStBA;IAPN,IAAI,CAACO,UAAUC,GAAG,EAAE;QAClB,OAAOC;IACT;IAEA,OAAOT,cAAcE,IAAI,KAAK,WAAWF,cAAcE,IAAI,KAAK,cAC5DF,cAAcU,cAAc,GAC5BV,cAAcE,IAAI,KAAK,YAAA,CACrBF,6BAAAA,cAAcW,OAAO,CAACC,GAAG,CAACC,kBAAAA,4BAA4B,CAAA,KAAA,OAAA,KAAA,IAAtDb,2BAAyDc,KAAK,GAC9DL;AACR;AAKO,SAASlB,kCACdgB,SAAoB,EACpBP,aAA4B;IAE5B,IAAIO,UAAUQ,WAAW,EAAE;QACzB,OAAQf,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOF,cAAcgB,SAAS;YAChC;gBACE,OAAOP;QACX;IACF;IAEA,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAqJKsD,QAAQC,GAAG,CAACC,QAAQ;AArJzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoVexD,QAAQ,EAAA;eAARA;;IA3CAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IAuKAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IAhXAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IAtWAC,qBAAqB,EAAA;eAArBA;;IAgSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IA3TAC,yBAAyB,EAAA;eAAzBA;;IAuPAC,oBAAoB,EAAA;eAApBA;;IAgSAC,wBAAwB,EAAA;eAAxBA;;IAvcAC,gCAAgC,EAAA;eAAhCA;;IA6ZAC,yBAAyB,EAAA;eAAzBA;;IApYAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IAmDHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DA9hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,wDACoB,iBACzBT,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACAR,oCAAoChB,OAAOR,YAAYoB;AACzD;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASvE,4CACd6C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMe,kBAAkBf,eAAeQ,UAAU,CAACQ,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1B,MAAM5B,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBlB,qBAAqB,GAAGS;gBACxCS,gBAAgBhB,yBAAyB,GAAGuC;gBAC5C,IAAIZ,eAAekB,UAAU,KAAK,MAAM;oBACtC,2EAA2E;oBAC3E,sEAAsE;oBACtE7B,gBAAgB8B,iBAAiB,GAAG;gBACtC;YACF;QACF;QACAf,oCAAoChB,OAAOR,YAAYoB;IACzD;IACA,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASrB,SAAS,EAAE+D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C+B;IACA,IAAI/B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACqD,qBAAqBjC,OAAOR;AACtD;AAEA,SAASyC,qBAAqBjC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY+B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBhC,IAAY+B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBlB,MAAc;IAC7C,OACEA,OAAOmB,QAAQ,CACb,sEAEFnB,OAAOmB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIV,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMc,6BAA6B;AAEnC,SAASlB,gCAAgCe,OAAe;IACtD,MAAMhB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BhB,MAAcoB,MAAM,GAAGD;IACzB,OAAOnB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcoB,MAAM,KAAKD,8BAC1B,UAAUnB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASlE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgByD,MAAM,GAAG;AAClC;AAEO,SAAShF,qBACdiF,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAc1D,eAAe,CAACwC,IAAI,IAAImB,cAAc3D,eAAe;IACnE,OAAO0D,cAAc1D,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJ4D,MAAM,CACL,CAACC,SACC,OAAOA,OAAOpC,KAAK,KAAK,YAAYoC,OAAOpC,KAAK,CAACgC,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEpD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLsC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAExD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASyB;IACP,IAAI,CAACtD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI6C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDe;IACA,MAAMZ,aAAa,IAAI6B;IACvB,qFAAqF;IACrF,IAAI;QACFtE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAOiC,GAAY;QACnB9B,WAAWC,KAAK,CAAC6B;IACnB;IACA,OAAO9B,WAAWQ,MAAM;AAC1B;AAOO,SAASlE,8BACdgC,aAAmC;IAEnC,MAAM0B,aAAa,IAAI6B;IAEvB,IAAIvD,cAAcyD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCzD,cAAcyD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1CjC,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1DiC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMlC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWQ,MAAM;AAC1B;AAEO,SAAStE,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,MAAM+D,YAAYC,0BAAAA,gBAAgB,CAAC1C,QAAQ;IAE3C,IACEyC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAMjE,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDhB,OAAAA,OAAK,CAACiF,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACnE,cAAcoE,YAAY,EAAEtE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9B1B,qBACEsF,UAAUvD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDxB,iCAAiCqB,YAAY+D,WAAW7D;YAC1D;QACF;IACF;AACF;AAEA,MAAMqE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAASlG,0BACd4B,KAAa,EACbuE,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAI4B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBrF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIgF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBtF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLsD,cAAcvD,yBAAyB,IACvCwD,cAAcxD,yBAAyB,EACvC;QACAuF,kBAAkBnF,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM6C,UAAU,CAAC,OAAO,EAAElC,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQwD,8BAA8BxC,SAASqC;QACrDC,kBAAkBlF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASwD,8BACPxC,OAAe,EACfqC,cAAsB;IAEtB,MAAMrD,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BhB,MAAMX,KAAK,GAAG,YAAY2B,UAAUqC;IACpC,OAAOrD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbwE,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAIkC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIrC,cAAcvD,yBAAyB,EAAE;QAC3C0F,YAAYnC,cAAcvD,yBAAyB;QACnD2F,iBAAiBpC,cAAczD,qBAAqB;QACpD8F,aAAarC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcxD,yBAAyB,EAAE;QAClD0F,YAAYlC,cAAcxD,yBAAyB;QACnD2F,iBAAiBnC,cAAc1D,qBAAqB;QACpD8F,aAAapC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL4C,YAAY;QACZC,iBAAiB5F;QACjB6F,aAAa;IACf;IAEA,IAAIL,kBAAkBnF,oBAAoB,IAAIsF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQ5D,KAAK,CAACyD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAI5E,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgBkF,kBAAkBlF,aAAa;IACrD,IAAIA,cAAciD,MAAM,EAAE;QACxB,IAAK,IAAIwC,IAAI,GAAGA,IAAIzF,cAAciD,MAAM,EAAEwC,IAAK;YAC7CD,QAAQ5D,KAAK,CAAC5B,aAAa,CAACyF,EAAE;QAChC;QAEA,MAAM,IAAIhF,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACyE,kBAAkBtF,mBAAmB,EAAE;QAC1C,IAAIsF,kBAAkBrF,kBAAkB,EAAE;YACxC,IAAIwF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIwE,kBAAkBpF,kBAAkB,EAAE;YAC/C,IAAIuF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2283, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/FYMCA%20SEM%204/Mock%20Screens/project/node_modules/next/src/server/dev/hot-reloader-types.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { Duplex } from 'stream'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type getBaseWebpackConfig from '../../build/webpack-config'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { Project, Update as TurbopackUpdate } from '../../build/swc/types'\nimport type { VersionInfo } from './parse-version-info'\nimport type { DebugInfo } from '../../client/components/react-dev-overlay/types'\nimport type { DevIndicatorServerState } from './dev-indicator-server-state'\n\nexport const enum HMR_ACTIONS_SENT_TO_BROWSER {\n  ADDED_PAGE = 'addedPage',\n  REMOVED_PAGE = 'removedPage',\n  RELOAD_PAGE = 'reloadPage',\n  SERVER_COMPONENT_CHANGES = 'serverComponentChanges',\n  MIDDLEWARE_CHANGES = 'middlewareChanges',\n  CLIENT_CHANGES = 'clientChanges',\n  SERVER_ONLY_CHANGES = 'serverOnlyChanges',\n  SYNC = 'sync',\n  BUILT = 'built',\n  BUILDING = 'building',\n  DEV_PAGES_MANIFEST_UPDATE = 'devPagesManifestUpdate',\n  TURBOPACK_MESSAGE = 'turbopack-message',\n  SERVER_ERROR = 'serverError',\n  TURBOPACK_CONNECTED = 'turbopack-connected',\n  ISR_MANIFEST = 'isrManifest',\n  DEV_INDICATOR = 'devIndicator',\n}\n\ninterface ServerErrorAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR\n  errorJSON: string\n}\n\nexport interface TurbopackMessageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE\n  data: TurbopackUpdate | TurbopackUpdate[]\n}\n\ninterface BuildingAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING\n}\n\nexport interface CompilationError {\n  moduleName?: string\n  message: string\n  details?: string\n  moduleTrace?: Array<{ moduleName?: string }>\n  stack?: string\n}\nexport interface SyncAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  versionInfo: VersionInfo\n  updatedModules?: ReadonlyArray<string>\n  debug?: DebugInfo\n  devIndicator: DevIndicatorServerState\n}\ninterface BuiltAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  updatedModules?: ReadonlyArray<string>\n}\n\ninterface AddedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE\n  data: [page: string | null]\n}\n\ninterface RemovedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE\n  data: [page: string | null]\n}\n\nexport interface ReloadPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE\n  data: string\n}\n\ninterface ServerComponentChangesAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES\n  hash: string\n}\n\ninterface MiddlewareChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES\n}\n\ninterface ClientChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES\n}\n\ninterface ServerOnlyChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES\n  pages: ReadonlyArray<string>\n}\n\ninterface DevPagesManifestUpdateAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE\n  data: [\n    {\n      devPagesManifest: true\n    },\n  ]\n}\n\nexport interface TurbopackConnectedAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n  data: { sessionId: number }\n}\n\nexport interface AppIsrManifestAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST\n  data: Record<string, boolean>\n}\n\nexport interface DevIndicatorAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_INDICATOR\n  devIndicator: DevIndicatorServerState\n}\n\nexport type HMR_ACTION_TYPES =\n  | TurbopackMessageAction\n  | TurbopackConnectedAction\n  | BuildingAction\n  | SyncAction\n  | BuiltAction\n  | AddedPageAction\n  | RemovedPageAction\n  | ReloadPageAction\n  | ServerComponentChangesAction\n  | ClientChangesAction\n  | MiddlewareChangesAction\n  | ServerOnlyChangesAction\n  | DevPagesManifestUpdateAction\n  | ServerErrorAction\n  | AppIsrManifestAction\n  | DevIndicatorAction\n\nexport type TurbopackMsgToBrowser =\n  | { type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE; data: any }\n  | {\n      type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n      data: { sessionId: number }\n    }\n\nexport interface NextJsHotReloaderInterface {\n  turbopackProject?: Project\n  activeWebpackConfigs?: Array<Awaited<ReturnType<typeof getBaseWebpackConfig>>>\n  serverStats: webpack.Stats | null\n  edgeServerStats: webpack.Stats | null\n  run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }>\n\n  setHmrServerError(error: Error | null): void\n  clearHmrServerError(): void\n  start(): Promise<void>\n  send(action: HMR_ACTION_TYPES): void\n  getCompilationErrors(page: string): Promise<any[]>\n  onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    onUpgrade: (client: { send(data: string): void }) => void\n  ): void\n  invalidate({\n    reloadAfterInvalidation,\n  }: {\n    reloadAfterInvalidation: boolean\n  }): Promise<void> | void\n  buildFallbackError(): Promise<void>\n  ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void>\n  close(): void\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER"], "mappings": ";;;;+BAWkBA,+BAAAA;;;eAAAA;;;AAAX,IAAWA,8BAAAA,WAAAA,GAAAA,SAAAA,2BAAAA;;;;;;;;;;;;;;;;;WAAAA", "ignoreList": [0], "debugId": null}}]}